{"name": "speechpilot", "version": "1.0.0", "description": "AI語音助手 - 桌面應用程式，支援語音輸入和AI指令處理", "main": "dist/src/main/main.js", "homepage": "./", "scripts": {"dev": "concurrently \"npm run dev:vite\" \"npm run dev:electron\"", "dev:vite": "vite", "dev:electron": "cross-env NODE_ENV=development electron .", "start": "npm run build && electron dist/src/main/main.js", "build": "npm run build:electron", "build:electron": "tsc -p tsconfig.main.json", "dist": "npm run build && electron-builder", "dist:win": "npm run build && electron-builder --win", "dist:mac": "npm run build && electron-builder --mac", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit"}, "keywords": ["electron", "speech-to-text", "ai", "voice-assistant", "azure", "openai"], "author": "SpeechPilot Team", "license": "MIT", "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.0", "concurrently": "^8.0.0", "cross-env": "^7.0.0", "electron": "^27.0.0", "electron-builder": "^24.0.0", "eslint": "^8.0.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "typescript": "^5.0.0", "vite": "^5.0.0", "vite-plugin-electron": "^0.15.0"}, "dependencies": {"@types/ws": "^8.18.1", "axios": "^1.6.0", "dotenv": "^17.2.0", "electron-store": "^10.1.0", "ffmpeg-static": "^5.2.0", "firebase": "^12.0.0", "ws": "^8.18.3"}, "build": {"appId": "com.speakoneai.app", "productName": "SpeakOneAI", "directories": {"output": "release"}, "files": ["dist/**/*", "assets/**/*"], "win": {"target": "nsis", "icon": "assets/logo5.png"}, "mac": {"target": "dmg", "icon": "assets/logo5.png", "category": "public.app-category.productivity"}, "protocols": [{"name": "SpeakOneAI OAuth", "schemes": ["speakoneai"]}], "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}