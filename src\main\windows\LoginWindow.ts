import { BrowserWindow, screen, ipcMain } from 'electron'
import { join } from 'path'

export interface LoginStatus {
  isLoading: boolean
  error?: string
  provider?: 'google' | 'facebook' | 'apple'
}

export class LoginWindow {
  private window: BrowserWindow | null = null
  private status: LoginStatus = {
    isLoading: false
  }

  create(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      console.log('🔐 Creating login window...')

      if (this.window) {
        console.log('🔐 Login window already exists, focusing...')
        this.window.focus()
        this.window.show()
        return
      }

      // 獲取主顯示器的工作區域
      const primaryDisplay = screen.getPrimaryDisplay()
      const { width, height } = primaryDisplay.workAreaSize
      console.log('🖥️ Display size:', { width, height })

      this.window = new BrowserWindow({
        width: 480,
        height: 600,
        x: Math.round((width - 480) / 2),
        y: Math.round((height - 600) / 2),
        resizable: false,
        minimizable: true,
        maximizable: false,
        alwaysOnTop: false,
        skipTaskbar: false,
        frame: true,
        transparent: false,
        backgroundColor: '#ffffff',
        show: false,
        focusable: true,
        movable: true,
        title: 'SpeechPilot - 登入',
        webPreferences: {
          nodeIntegration: true,
          contextIsolation: false,
          zoomFactor: 1.0
        }
      })

      console.log('🔐 Login BrowserWindow created')

      // 載入登入 HTML 文件
      const htmlPath = join(process.cwd(), 'src/renderer/login.html')
      console.log('🔐 Loading login HTML file:', htmlPath)
      
      // 添加事件監聽器
      this.window.webContents.on('did-start-loading', () => {
        console.log('🔄 Login window started loading')
      })

      this.window.webContents.on('did-finish-load', () => {
        console.log('✅ Login window finished loading')
      })

      this.window.webContents.on('did-fail-load', (_event, errorCode, errorDescription) => {
        console.error('❌ Login window failed to load:', errorCode, errorDescription)
        reject(new Error(`Failed to load login window: ${errorDescription}`))
      })

      this.window.on('ready-to-show', () => {
        console.log('🔐 Login window ready to show')
        this.window?.show()
      })

      // 設置 IPC 處理程序
      this.setupIPC(resolve, reject)

      this.window.loadFile(htmlPath)
        .then(() => {
          console.log('🔐 Login HTML loaded successfully')
          if (this.window && !this.window.isDestroyed()) {
            this.window.show()
            console.log('🔐 Login window shown')
          }
        })
        .catch((error) => {
          console.error('❌ Failed to load login HTML:', error)
          reject(error)
        })

      // 窗口關閉事件
      this.window.on('closed', () => {
        console.log('🔐 Login window closed')
        this.window = null
        // 如果用戶關閉登入視窗而沒有登入，則拒絕 Promise
        reject(new Error('Login window closed by user'))
      })

      console.log('🔐 Login window created')
    })
  }

  private setupIPC(resolve: (value: boolean) => void, reject: (reason?: any) => void) {
    // 移除現有的處理程序（如果存在）
    ipcMain.removeHandler('login-with-google')
    ipcMain.removeHandler('login-with-facebook')
    ipcMain.removeHandler('login-with-apple')
    ipcMain.removeHandler('close-login-window')

    // Google 登入
    ipcMain.handle('login-with-google', async () => {
      try {
        console.log('🔐 Google login requested')
        this.updateStatus({ isLoading: true, provider: 'google' })
        
        // 這裡會調用 ServiceManager 的 Google 登入方法
        const { getServiceManager } = require('../services/ServiceManager')
        const serviceManager = getServiceManager()
        const success = await serviceManager.signInWithGoogle()
        
        if (success) {
          console.log('✅ Google login successful')
          this.close()
          resolve(true)
          return { success: true }
        } else {
          this.updateStatus({ isLoading: false, error: 'Google 登入失敗' })
          return { success: false, error: 'Google 登入失敗' }
        }
      } catch (error) {
        console.error('❌ Google login error:', error)
        this.updateStatus({ isLoading: false, error: '登入過程中發生錯誤' })
        return { success: false, error: '登入過程中發生錯誤' }
      }
    })

    // Facebook 登入
    ipcMain.handle('login-with-facebook', async () => {
      try {
        console.log('🔐 Facebook login requested')
        this.updateStatus({ isLoading: true, provider: 'facebook' })
        
        const { getServiceManager } = require('../services/ServiceManager')
        const serviceManager = getServiceManager()
        const success = await serviceManager.signInWithFacebook()
        
        if (success) {
          console.log('✅ Facebook login successful')
          this.close()
          resolve(true)
          return { success: true }
        } else {
          this.updateStatus({ isLoading: false, error: 'Facebook 登入失敗' })
          return { success: false, error: 'Facebook 登入失敗' }
        }
      } catch (error) {
        console.error('❌ Facebook login error:', error)
        this.updateStatus({ isLoading: false, error: '登入過程中發生錯誤' })
        return { success: false, error: '登入過程中發生錯誤' }
      }
    })

    // Apple 登入
    ipcMain.handle('login-with-apple', async () => {
      try {
        console.log('🔐 Apple login requested')
        this.updateStatus({ isLoading: true, provider: 'apple' })
        
        const { getServiceManager } = require('../services/ServiceManager')
        const serviceManager = getServiceManager()
        const success = await serviceManager.signInWithApple()
        
        if (success) {
          console.log('✅ Apple login successful')
          this.close()
          resolve(true)
          return { success: true }
        } else {
          this.updateStatus({ isLoading: false, error: 'Apple 登入失敗' })
          return { success: false, error: 'Apple 登入失敗' }
        }
      } catch (error) {
        console.error('❌ Apple login error:', error)
        this.updateStatus({ isLoading: false, error: '登入過程中發生錯誤' })
        return { success: false, error: '登入過程中發生錯誤' }
      }
    })

    // 關閉登入視窗
    ipcMain.handle('close-login-window', () => {
      console.log('🔐 Close login window requested')
      this.close()
      reject(new Error('Login cancelled by user'))
    })
  }

  updateStatus(status: Partial<LoginStatus>): void {
    this.status = { ...this.status, ...status }
    
    if (this.window && !this.window.isDestroyed()) {
      this.window.webContents.send('login-status-update', this.status)
    }
  }

  close(): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.close()
    }
  }

  isVisible(): boolean {
    return this.window !== null && !this.window.isDestroyed()
  }

  focus(): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.focus()
    }
  }

  getWindow(): BrowserWindow | null {
    return this.window
  }
}
