// 訂閱計劃配置 - 客戶端使用，不包含 Stripe 相關邏輯
// Stripe 支付邏輯在 SpeechPilot Web 中處理

export type SubscriptionPlan = 'FREE' | 'STARTER' | 'PRO' | 'PREMIUM' | 'MAX'
export type Platform = 'windows' | 'macos' | 'ios' | 'android'

// 訂閱計劃配置（客戶端版本）
export interface PlanConfig {
  name: string
  dailyLimitSeconds: number // -1 表示無限制
  maxDevices: number // -1 表示無限制
  platforms: Platform[]
  features: string[]
  // 注意：價格和 Stripe 相關資訊由 Web 端處理
}

export const SUBSCRIPTION_PLANS: Record<SubscriptionPlan, PlanConfig> = {
  FREE: {
    name: 'Free',
    dailyLimitSeconds: 5 * 60, // 5 分鐘
    maxDevices: 1,
    platforms: ['windows', 'macos'],
    features: [
      '每日 5 分鐘使用時間',
      '支援 Windows 和 macOS',
      '最多 1 台設備'
    ]
  },
  STARTER: {
    name: 'Starter',
    dailyLimitSeconds: 60 * 60, // 1 小時
    maxDevices: 1,
    platforms: ['windows', 'macos'],
    features: [
      '每日 1 小時使用時間',
      '支援 Windows 和 macOS',
      '最多 1 台設備',
      '優先客戶支援'
    ]
  },
  PRO: {
    name: 'Pro',
    dailyLimitSeconds: 2 * 60 * 60, // 2 小時
    maxDevices: 2,
    platforms: ['windows', 'macos', 'ios', 'android'],
    features: [
      '每日 2 小時使用時間',
      '支援所有平台',
      '最多 2 台設備',
      '進階功能存取',
      '優先客戶支援'
    ]
  },
  PREMIUM: {
    name: 'Premium',
    dailyLimitSeconds: 8 * 60 * 60, // 8 小時
    maxDevices: 5,
    platforms: ['windows', 'macos', 'ios', 'android'],
    features: [
      '每日 8 小時使用時間',
      '支援所有平台',
      '最多 5 台設備',
      '所有進階功能',
      '專屬客戶支援',
      'API 存取權限'
    ]
  },
  MAX: {
    name: 'Max',
    dailyLimitSeconds: -1, // 無限制
    maxDevices: -1, // 無限制
    platforms: ['windows', 'macos', 'ios', 'android'],
    features: [
      '無限使用時間',
      '支援所有平台',
      '無限設備數量',
      '所有功能完整存取',
      '24/7 專屬支援',
      '完整 API 存取',
      '企業級功能'
    ]
  }
}

// 檢查用戶是否可以存取特定平台
export const canAccessPlatform = (plan: SubscriptionPlan, platform: Platform): boolean => {
  const planConfig = SUBSCRIPTION_PLANS[plan]
  return planConfig.platforms.includes(platform)
}

// 獲取計劃的每日限制（秒）
export const getDailyLimitSeconds = (plan: SubscriptionPlan): number => {
  return SUBSCRIPTION_PLANS[plan].dailyLimitSeconds
}

// 獲取計劃的最大設備數量
export const getMaxDevices = (plan: SubscriptionPlan): number => {
  return SUBSCRIPTION_PLANS[plan].maxDevices
}

// 檢查是否為付費計劃
export const isPaidPlan = (plan: SubscriptionPlan): boolean => {
  return plan !== 'FREE'
}

// 獲取計劃升級建議
export const getUpgradeRecommendation = (currentPlan: SubscriptionPlan, dailyUsageSeconds: number): SubscriptionPlan | null => {
  const currentLimit = SUBSCRIPTION_PLANS[currentPlan].dailyLimitSeconds
  
  // 如果當前計劃已經是無限制，不需要升級
  if (currentLimit === -1) return null
  
  // 如果使用量超過當前限制的 80%，建議升級
  if (dailyUsageSeconds > currentLimit * 0.8) {
    const plans: SubscriptionPlan[] = ['FREE', 'STARTER', 'PRO', 'PREMIUM', 'MAX']
    const currentIndex = plans.indexOf(currentPlan)
    
    // 找到下一個可用的計劃
    for (let i = currentIndex + 1; i < plans.length; i++) {
      const nextPlan = plans[i]
      const nextLimit = SUBSCRIPTION_PLANS[nextPlan].dailyLimitSeconds
      
      if (nextLimit === -1 || dailyUsageSeconds <= nextLimit * 0.8) {
        return nextPlan
      }
    }
  }
  
  return null
}

// 驗證計劃是否有效
export const isValidPlan = (plan: string): plan is SubscriptionPlan => {
  return Object.keys(SUBSCRIPTION_PLANS).includes(plan)
}

// 獲取所有可用計劃
export const getAllPlans = (): SubscriptionPlan[] => {
  return Object.keys(SUBSCRIPTION_PLANS) as SubscriptionPlan[]
}

// 獲取付費計劃
export const getPaidPlans = (): SubscriptionPlan[] => {
  return getAllPlans().filter(plan => isPaidPlan(plan))
}
