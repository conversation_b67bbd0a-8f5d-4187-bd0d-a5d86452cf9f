# 📋 SpeechPilot Backend API 規格文檔

**🌏 部署區域**: Asia East (asia-east1)
**🔗 API 基礎 URL**: `https://asia-east1-speechpilot-f1495.cloudfunctions.net`
**🚀 運行時**: Python 3.11 (Firebase Functions v2)

## 🔄 應用程式流程圖

```mermaid
graph TD
    A[用戶啟動應用] --> B[Firebase Auth 登入]
    B --> C{登入成功?}
    C -->|否| D[顯示登入錯誤]
    C -->|是| E[觸發 on_user_created]
    E --> F[初始化用戶資料]
    F --> G[收集設備資訊]
    G --> H[調用 register_device_v2]
    H --> I{設備註冊成功?}
    I -->|否| J[處理註冊錯誤]
    I -->|是| K[調用 validate_device]
    K --> L{設備驗證通過?}
    L -->|否| M[顯示存取限制]
    L -->|是| N[開始使用應用功能]
    N --> O[調用 submit_usage]
    O --> P[更新使用統計]
    P --> Q{繼續使用?}
    Q -->|是| N
    Q -->|否| R[調用 end_session]
    R --> S[結束應用]
```

## 🎯 核心功能流程

### 1. 用戶認證流程
```
用戶登入 → Firebase Auth → on_user_created (Trigger) → 初始化用戶資料
```

**觸發的 Function**: `on_user_created`
- **類型**: Firestore Trigger
- **觸發條件**: 新用戶文檔創建時
- **功能**: 初始化用戶預設資料和使用限制

### 2. 設備註冊流程
```
應用啟動 → 收集設備資訊 → register_device_v2 → 設備驗證 → 開始使用
```

**主要 Function**: `register_device_v2`
- **類型**: HTTPS Callable
- **輸入**: 設備ID、平台、設備資訊
- **功能**: 註冊新設備或更新現有設備
- **檢查**: 設備數量限制、平台支援

### 3. 設備存取驗證流程
```
功能使用前 → validate_device → 檢查限制 → 創建使用會話 → 允許/拒絕存取
```

**主要 Function**: `validate_device`
- **類型**: HTTPS Callable
- **輸入**: 設備ID、平台
- **功能**: 驗證設備授權和使用限制
- **檢查**: 設備註冊狀態、同時使用限制、平台支援

### 4. 使用量追蹤流程
```
使用功能 → submit_usage → 更新統計 → 檢查限制 → 返回剩餘額度
```

**主要 Function**: `submit_usage`
- **類型**: HTTPS Callable
- **輸入**: 使用時長、功能類型
- **功能**: 記錄使用量並檢查限制
- **更新**: 日使用量、月使用量

### 5. 會話管理流程
```
結束使用 → end_session → 清理會話 → 釋放同時使用限制
```

**主要 Function**: `end_session`
- **類型**: HTTPS Callable
- **輸入**: 設備ID
- **功能**: 結束設備使用會話
- **清理**: 活躍會話標記

## 📱 客戶端整合規格

### 1. 應用啟動序列
```typescript
import { getFunctions, httpsCallable } from 'firebase/functions';

// 1. 初始化 Firebase Functions (指定 Asia East 區域)
const functions = getFunctions(app, 'asia-east1');

// 2. Firebase Auth 登入
const user = await signInWithEmailAndPassword(auth, email, password);

// 3. 收集設備資訊
const deviceInfo = collectDeviceInfo();

// 4. 註冊設備
const registerDevice = httpsCallable(functions, 'register_device_v2');
const registerResult = await registerDevice({
  device_id: getDeviceUUID(),
  device_name: getDeviceName(),
  platform: getPlatform(), // 'windows' | 'macos' | 'ios' | 'android'
  app_version: getAppVersion(),
  device_info: deviceInfo
});

// 5. 驗證設備存取
if (registerResult.data.success) {
  const validateDevice = httpsCallable(functions, 'validate_device');
  const accessResult = await validateDevice({
    device_id: getDeviceUUID(),
    platform: getPlatform(),
    app_version: getAppVersion()
  });

  if (accessResult.data.has_access) {
    // 開始使用應用
    startApp();
  } else {
    // 處理存取限制
    handleAccessDenied(accessResult.data);
  }
}
```

### 2. 功能使用序列
```typescript
// 1. 開始使用功能前驗證
const canUse = await validateDeviceAccess();
if (!canUse) return;

// 2. 使用功能
const startTime = Date.now();
await useSpeechToTextFeature();
const endTime = Date.now();

// 3. 提交使用量
const submitUsage = httpsCallable(functions, 'submit_usage');
const usageResult = await submitUsage({
  duration_seconds: Math.floor((endTime - startTime) / 1000),
  feature_type: 'ai-speech-to-text',
  device_id: getDeviceUUID()
});

// 4. 檢查剩餘額度
if (usageResult.data.limit_exceeded) {
  showLimitExceededMessage();
}
```

### 3. 應用結束序列
```typescript
// 結束會話
const endSession = httpsCallable(functions, 'end_session');
await endSession({
  device_id: getDeviceUUID()
});
```

## 🔧 Firebase Functions 列表

### HTTPS Callable Functions

| Function Name | 用途 | 輸入參數 | 返回值 |
|---------------|------|----------|--------|
| `verify_token` | 驗證 API Token | - | token_valid, user_id |
| `check_user_subscription_status` | 檢查訂閱狀態 | - | subscription, status |
| `register_device_v2` | 設備註冊（統一接口） | device_id, platform, device_info | success, device_count |
| `validate_device` | 設備存取驗證 | device_id, platform | has_access, reason |
| `check_usage_before_recording` | 錄音前檢查 | device_id, features | can_use, remaining_seconds |
| `calculate_usage_after_recording` | 錄音後計算 | device_id, session_data | calculated_seconds, cost |
| `submit_usage` | 提交使用量 | duration_seconds, feature_type | remaining_quota |
| `end_session` | 結束使用會話 | device_id | success |
| `get_user_info` | 獲取用戶資訊 | - | user_data, usage_stats |
| `get_devices_info` | 獲取設備資訊 | - | devices, plan_info |

### Firestore Triggers

| Trigger Name | 觸發條件 | 功能 |
|--------------|----------|------|
| `on_user_created` | 新用戶創建 | 初始化用戶資料 |

## 🚨 錯誤處理規格

### 1. 設備註冊錯誤
```typescript
// 錯誤代碼和處理
switch (error.code) {
  case 'DEVICE_LIMIT_EXCEEDED':
    // 顯示設備管理頁面，讓用戶移除舊設備
    showDeviceManagement();
    break;
  case 'PLATFORM_NOT_SUPPORTED':
    // 顯示升級計劃提示
    showUpgradePrompt();
    break;
  case 'INVALID_DEVICE':
    // 重新收集設備資訊
    reCollectDeviceInfo();
    break;
}
```

### 2. 存取驗證錯誤
```typescript
// 存取被拒絕的處理
switch (result.reason) {
  case 'DEVICE_NOT_REGISTERED':
    // 重新註冊設備
    await registerDevice();
    break;
  case 'CONCURRENT_LIMIT_EXCEEDED':
    // 顯示其他活躍會話，讓用戶選擇結束
    showActiveSessions(result.active_sessions);
    break;
}
```

## 📊 監控和分析規格

### 1. 關鍵指標追蹤
- 設備註冊成功率
- 設備驗證通過率
- 使用量限制觸發次數
- 平台使用分佈
- 同時使用會話數量

### 2. 錯誤監控
- Function 執行錯誤率
- 設備註冊失敗原因分析
- 存取被拒絕原因統計

## 🔐 安全性規格

### 1. 認證要求
- 所有 Callable Functions 都需要 Firebase Auth 認證
- 設備ID 必須是有效的 UUID 格式
- 平台參數必須是支援的值

### 2. 資料驗證
- 輸入參數格式驗證
- 設備資訊完整性檢查
- 使用量數據合理性驗證

### 3. 權限控制
- 用戶只能操作自己的設備
- 設備只能由註冊用戶使用
- 管理員功能需要特殊權限

## 🚀 部署規格

### 1. 環境要求
- Python 3.11+
- Firebase CLI
- Firebase Admin SDK

### 2. 部署步驟
```bash
# 1. 安裝依賴
cd backend-api
pip install -r requirements.txt

# 2. 部署到 Asia East 區域
firebase deploy --only functions:backend-api

# 3. 驗證部署
firebase functions:list | grep asia-east1
```

### 3. 環境變數
- Firebase 專案配置
- 服務帳戶金鑰
- 功能開關設定

這個規格文檔定義了完整的應用程式流程和 Firebase Functions 整合方案，確保開發團隊能夠正確實作和整合所有功能。
