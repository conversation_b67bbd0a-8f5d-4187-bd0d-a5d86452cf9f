// import { httpsCallable } from 'firebase/functions' // 已移至 FirebaseFunctionLogger
// import { functions } from '../../../firebase' // 已移至 FirebaseFunctionLogger
import { AuthService } from './AuthService'
import { platform } from 'os'
import { app } from 'electron'
import { FirebaseFunctionLogger } from '../utils/FirebaseFunctionLogger'

export interface UsageSession {
  sessionId: string
  deviceId: string
  sessionStart: Date
  sessionEnd?: Date
  secondsUsed: number
  platform: 'windows' | 'macos' | 'ios' | 'android'
  appVersion: string
  features: string[]
}

export interface DeviceInfo {
  deviceId: string
  deviceName: string
  platform: 'windows' | 'macos' | 'ios' | 'android'
  deviceFingerprint: string
  appVersion: string
  osVersion: string
}

export class UsageTrackingService {
  private authService: AuthService
  private currentSession: UsageSession | null = null
  private lastSessionData: any = null
  private deviceInfo: DeviceInfo | null = null
  private sessionTimer: NodeJS.Timeout | null = null
  private isRegistered = false

  constructor(authService: AuthService) {
    this.authService = authService
    this.initializeDevice()
  }

  private async initializeDevice() {
    try {
      // 生成設備資訊
      this.deviceInfo = await this.generateDeviceInfo()

      // 監聽認證狀態變化 - 移除自動設備註冊
      this.authService.onAuthStateChanged(async (authState) => {
        if (!authState.isAuthenticated) {
          // 用戶登出時重置狀態
          this.isRegistered = false
          await this.endCurrentSession()
        }
        // 移除自動設備註冊，改為在 ServiceManager 中手動控制
      })

    } catch (error) {
      console.error('設備初始化失敗:', error)
    }
  }

  private async generateDeviceInfo(): Promise<DeviceInfo> {
    const os = platform()
    const platformMap: Record<string, 'windows' | 'macos' | 'ios' | 'android'> = {
      'win32': 'windows',
      'darwin': 'macos',
      'linux': 'windows' // 暫時歸類為 windows
    }

    // 生成設備指紋（基於硬體資訊）
    const deviceFingerprint = await this.generateDeviceFingerprint()

    // 生成設備 ID（持久化存儲）
    const deviceId = await this.getOrCreateDeviceId()

    // 生成友好的設備名稱
    const deviceName = await this.generateFriendlyDeviceName(os)

    return {
      deviceId,
      deviceName,
      platform: platformMap[os] || 'windows',
      deviceFingerprint,
      appVersion: app.getVersion(),
      osVersion: process.getSystemVersion()
    }
  }

  // 生成友好的設備名稱
  private async generateFriendlyDeviceName(os: string): Promise<string> {
    try {
      // 嘗試獲取當前用戶的顯示名稱
      const currentUser = this.authService.getCurrentUser()
      let userName = ''

      if (currentUser?.displayName) {
        userName = currentUser.displayName
      } else if (currentUser?.email) {
        // 從 email 提取用戶名
        userName = currentUser.email.split('@')[0]
      }

      // 獲取系統用戶名作為備用
      let systemUserName = ''
      try {
        if (os === 'win32') {
          systemUserName = process.env.USERNAME || process.env.USER || ''
        } else if (os === 'darwin' || os === 'linux') {
          systemUserName = process.env.USER || process.env.LOGNAME || ''
        }
      } catch (e) {
        console.warn('無法獲取系統用戶名:', e)
      }

      // 優先使用 Firebase 用戶名，其次系統用戶名，最後使用預設名稱
      const finalUserName = userName || systemUserName || 'User'

      // 生成設備名稱
      const platformName = os === 'win32' ? 'Windows' :
                          os === 'darwin' ? 'macOS' :
                          os === 'linux' ? 'Linux' : 'Desktop'

      return `${finalUserName}'s ${platformName}`

    } catch (error) {
      console.warn('生成友好設備名稱失敗，使用預設名稱:', error)
      const platformName = os === 'win32' ? 'Windows' :
                          os === 'darwin' ? 'macOS' :
                          os === 'linux' ? 'Linux' : 'Desktop'
      return `${platformName} Device`
    }
  }

  private async generateDeviceFingerprint(): Promise<string> {
    try {
      // 使用系統資訊生成設備指紋
      const { execSync } = require('child_process')
      const crypto = require('crypto')
      
      let fingerprint = ''
      
      if (platform() === 'win32') {
        try {
          const wmic = execSync('wmic csproduct get uuid', { encoding: 'utf8' })
          fingerprint = wmic.split('\n')[1]?.trim() || ''
        } catch (e) {
          fingerprint = process.env.COMPUTERNAME || 'unknown'
        }
      } else if (platform() === 'darwin') {
        try {
          const serial = execSync('system_profiler SPHardwareDataType | grep "Serial Number"', { encoding: 'utf8' })
          fingerprint = serial.split(':')[1]?.trim() || ''
        } catch (e) {
          fingerprint = process.env.USER || 'unknown'
        }
      }
      
      // 如果無法獲取硬體資訊，使用其他方式
      if (!fingerprint) {
        fingerprint = `${platform()}-${process.arch}-${process.env.USERNAME || process.env.USER || 'unknown'}`
      }
      
      // 生成 hash
      return crypto.createHash('sha256').update(fingerprint).digest('hex').substring(0, 16)
      
    } catch (error) {
      console.error('生成設備指紋失敗:', error)
      // 回退方案
      const crypto = require('crypto')
      const fallback = `${platform()}-${Date.now()}-${Math.random()}`
      return crypto.createHash('sha256').update(fallback).digest('hex').substring(0, 16)
    }
  }

  private async getOrCreateDeviceId(): Promise<string> {
    try {
      // 使用 Node.js fs 持久化存儲設備 ID
      const fs = require('fs')
      const path = require('path')
      const { app } = require('electron')

      const userDataPath = app.getPath('userData')
      const deviceConfigPath = path.join(userDataPath, 'device-config.json')

      let deviceId: string

      if (fs.existsSync(deviceConfigPath)) {
        const config = JSON.parse(fs.readFileSync(deviceConfigPath, 'utf8'))
        deviceId = config.deviceId
      } else {
        const crypto = require('crypto')
        deviceId = crypto.randomUUID()
        const config = { deviceId }
        fs.writeFileSync(deviceConfigPath, JSON.stringify(config, null, 2))
      }

      return deviceId
    } catch (error) {
      console.error('獲取設備 ID 失敗:', error)
      // 回退方案：生成臨時 ID
      const crypto = require('crypto')
      return crypto.randomUUID()
    }
  }

  // 註冊設備
  async registerDevice(): Promise<boolean> {
    try {
      if (!this.authService.isAuthenticated() || !this.deviceInfo) {
        return false
      }

      // 轉換為新 API 格式
      const deviceData = {
        device_id: this.deviceInfo.deviceId,
        device_name: this.deviceInfo.deviceName,
        platform: this.deviceInfo.platform, // 已經在 generateDeviceInfo 中標準化
        app_version: this.deviceInfo.appVersion,
        device_info: {
          os_version: this.deviceInfo.osVersion,
          fingerprint: this.deviceInfo.deviceFingerprint
        }
      }

      console.log('📱 準備註冊設備:', deviceData)

      // 使用增強的日誌包裝器調用 Firebase Function
      const result = await FirebaseFunctionLogger.call('register_device_v2', deviceData)

      if (result.data?.success) {
        this.isRegistered = true
        console.log(`✅ 設備註冊成功: ${result.data.data.device_count}/${result.data.data.max_devices}`)

        // 移除自動驗證，改為在 ServiceManager 中手動控制
        return true
      }

      return false
    } catch (error) {
      console.error('設備註冊失敗:', error)
      this.handleDeviceError(error)
      return false
    }
  }

  // 驗證設備存取權限
  async validateDevice(): Promise<boolean> {
    try {
      if (!this.deviceInfo) {
        return false
      }

      const validateData = {
        device_id: this.deviceInfo.deviceId,
        platform: this.deviceInfo.platform
      }

      console.log('🔍 準備驗證設備:', validateData)

      // 使用增強的日誌包裝器調用 Firebase Function
      const result = await FirebaseFunctionLogger.call('validate_device', validateData)

      if (result.data?.success && result.data.data?.has_access) {
        console.log('✅ 設備驗證成功，可以使用應用')
        return true
      } else {
        console.log('❌ 設備存取被拒絕:', result.data?.data?.reason)
        return false
      }

    } catch (error) {
      console.error('❌ 設備驗證失敗:', error)
      return false
    }
  }

  // 處理設備相關錯誤
  private handleDeviceError(error: any): void {
    switch (error.code) {
      case 'functions/resource-exhausted':
        console.error('❌ 已達到設備數量限制，請升級您的計劃')
        break
      case 'functions/permission-denied':
        if (error.message?.includes('平台')) {
          console.error('❌ 當前計劃不支援此平台')
        } else {
          console.error('❌ 設備所有權驗證失敗')
        }
        break
      default:
        console.error(`❌ 設備錯誤: ${error.message}`)
    }
  }

  // 開始使用會話
  async startSession(features: string[] = []): Promise<boolean> {
    try {
      if (!this.authService.isAuthenticated() || !this.deviceInfo) {
        throw new Error('用戶未認證或設備未初始化')
      }

      if (!this.isRegistered) {
        const registered = await this.registerDevice()
        if (!registered) {
          throw new Error('設備註冊失敗')
        }
      }

      // 結束當前會話（如果存在）
      await this.endCurrentSession()

      // 創建新會話
      const crypto = require('crypto')
      this.currentSession = {
        sessionId: crypto.randomUUID(),
        deviceId: this.deviceInfo.deviceId,
        sessionStart: new Date(),
        secondsUsed: 0,
        platform: this.deviceInfo.platform,
        appVersion: this.deviceInfo.appVersion,
        features
      }

      // 開始計時
      this.startSessionTimer()
      
      console.log('使用會話開始:', this.currentSession.sessionId)
      return true
      
    } catch (error) {
      console.error('開始會話失敗:', error)
      return false
    }
  }

  // 結束使用會話
  async endSession(): Promise<boolean> {
    return await this.endCurrentSession()
  }

  private async endCurrentSession(): Promise<boolean> {
    try {
      if (!this.currentSession) {
        return true
      }

      // 停止計時器
      if (this.sessionTimer) {
        clearInterval(this.sessionTimer)
        this.sessionTimer = null
      }

      // 計算使用時間
      const sessionEnd = new Date()
      const secondsUsed = Math.floor((sessionEnd.getTime() - this.currentSession.sessionStart.getTime()) / 1000)

      // 保存最後會話資料供 ServiceManager 使用
      this.lastSessionData = {
        deviceId: this.currentSession.deviceId,
        sessionStart: this.currentSession.sessionStart,
        sessionEnd,
        features: this.currentSession.features,
        platform: this.currentSession.platform,
        appVersion: this.currentSession.appVersion,
        secondsUsed
      }

      this.currentSession = null
      console.log('使用會話結束，使用時間:', secondsUsed, '秒')
      return true
      
    } catch (error) {
      console.error('結束會話失敗:', error)
      this.currentSession = null
      return false
    }
  }



  // 開始會話計時器
  private startSessionTimer() {
    if (this.sessionTimer) {
      clearInterval(this.sessionTimer)
    }

    // 每秒更新一次使用時間
    this.sessionTimer = setInterval(() => {
      if (this.currentSession) {
        const now = new Date()
        this.currentSession.secondsUsed = Math.floor(
          (now.getTime() - this.currentSession.sessionStart.getTime()) / 1000
        )
      }
    }, 1000)
  }

  // 獲取當前會話資訊
  getCurrentSession(): UsageSession | null {
    return this.currentSession ? { ...this.currentSession } : null
  }

  // 獲取設備資訊
  getDeviceInfo(): DeviceInfo | null {
    return this.deviceInfo ? { ...this.deviceInfo } : null
  }

  // 檢查是否有活動會話
  hasActiveSession(): boolean {
    return this.currentSession !== null
  }

  // 獲取最後會話資料（供 ServiceManager 使用）
  getLastSessionData(): any {
    return this.lastSessionData
  }

  // 清理最後會話資料
  clearLastSessionData(): void {
    this.lastSessionData = null
  }

  // 工作流程 2: 錄音前檢查
  async checkUsageBeforeRecording(_estimatedDurationSeconds: number = 60): Promise<{
    canStart: boolean
    availableSeconds: number
    message?: string
    upgradeUrl?: string
  } | null> {
    try {
      if (!this.authService.isAuthenticated()) {
        throw new Error('用戶未認證')
      }

      const checkData = {
        device_id: this.deviceInfo?.deviceId,
        features: ['ai-speech-to-text']
      }

      console.log('⏰ 準備檢查錄音前使用量:', checkData)

      // 使用增強的日誌包裝器調用 Firebase Function
      const result = await FirebaseFunctionLogger.call('check_usage_before_recording', checkData)

      if (result.data?.success) {
        const data = result.data.data
        return {
          canStart: data.can_use,
          availableSeconds: data.remaining_seconds,
          message: data.can_use ? '可以開始錄音' : '使用時間已用完'
        }
      } else {
        return {
          canStart: false,
          availableSeconds: 0,
          message: result.data?.message || '使用時間已用完，請升級您的訂閱計劃'
        }
      }

    } catch (error) {
      console.error('錄音前檢查失敗:', error)
      return null
    }
  }

  // 提交使用量記錄
  async submitUsage(durationSeconds: number, featureType: string = 'ai-speech-to-text'): Promise<boolean> {
    try {
      if (!this.authService.isAuthenticated()) {
        throw new Error('用戶未認證')
      }

      const usageData = {
        duration_seconds: durationSeconds,
        feature_type: featureType,
        device_id: this.deviceInfo?.deviceId
      }

      console.log('📊 準備提交使用量:', usageData)

      // 使用增強的日誌包裝器調用 Firebase Function
      const result = await FirebaseFunctionLogger.call('submit_usage', usageData)

      if (result.data?.success) {
        const data = result.data.data
        console.log(`📊 使用量已記錄: ${durationSeconds} 秒`)
        console.log(`⏰ 今日剩餘: ${data.remaining_quota} 秒`)

        if (data.limit_exceeded) {
          console.warn('⚠️ 今日使用量已達上限')
        }

        return true
      }

      return false

    } catch (error) {
      console.error('❌ 使用量提交失敗:', error)
      // 不阻止用戶繼續使用，但記錄錯誤
      return false
    }
  }

  // 結束會話並提交使用量
  async endSessionAndSubmitUsage(): Promise<boolean> {
    try {
      if (!this.currentSession) {
        return false
      }

      // 結束會話
      const success = await this.endCurrentSession()

      if (success && this.lastSessionData) {
        // 提交使用量
        await this.submitUsage(this.lastSessionData.secondsUsed)
        this.clearLastSessionData()
      }

      return success

    } catch (error) {
      console.error('❌ 結束會話並提交使用量失敗:', error)
      return false
    }
  }

  // 清理資源
  destroy() {
    this.endCurrentSession()
    if (this.sessionTimer) {
      clearInterval(this.sessionTimer)
      this.sessionTimer = null
    }
    this.lastSessionData = null
  }
}
