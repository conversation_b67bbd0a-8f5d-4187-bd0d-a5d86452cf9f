import { BrowserWindow, screen, ipcMain } from 'electron'

export interface SettingsData {
  audioDevice: string
  hotkeys: {
    ai: string
    direct: string
  }
  volume: {
    threshold: number
  }
  autoStart: boolean
  languages: string[]
  recordingTimeout: number
  hotkeyMode: 'toggle' | 'hold'
}

export class SettingsWindow {
  private window: BrowserWindow | null = null
  private store: any = null

  constructor() {
    this.initStore()
  }

  private async initStore() {
    try {
      // 使用簡單的 JSON 文件存儲，避免 ESM 問題
      const fs = require('fs')
      const path = require('path')
      const { app } = require('electron')

      const settingsPath = path.join(app.getPath('userData'), 'settings.json')

      // 默認設置
      const defaultSettings = {
        audioDevice: 'default',
        hotkeys: {
          ai: 'CommandOrControl+Shift+C',
          direct: 'CommandOrControl+Shift+V'
        },
        volume: {
          threshold: 10
        },
        autoStart: false,
        languages: ['Chinese', 'English'],
        recordingTimeout: 5,
        hotkeyMode: 'toggle' as const
      }

      // 創建簡單的 store 對象
      this.store = {
        get: (key: string) => {
          try {
            if (fs.existsSync(settingsPath)) {
              const data = JSON.parse(fs.readFileSync(settingsPath, 'utf8'))
              return data[key] || defaultSettings[key as keyof typeof defaultSettings]
            }
            return defaultSettings[key as keyof typeof defaultSettings]
          } catch {
            return defaultSettings[key as keyof typeof defaultSettings]
          }
        },
        set: (key: string, value: any) => {
          try {
            let data: any = {}
            if (fs.existsSync(settingsPath)) {
              data = JSON.parse(fs.readFileSync(settingsPath, 'utf8'))
            }
            data[key] = value
            fs.writeFileSync(settingsPath, JSON.stringify(data, null, 2))
          } catch (error) {
            console.error('❌ Failed to save setting:', error)
          }
        }
      }

      console.log('⚙️ Simple JSON store initialized successfully')
    } catch (error) {
      console.error('❌ Failed to initialize store:', error)
    }
  }

  show() {
    if (this.window) {
      this.window.focus()
      return
    }

    const { width, height } = screen.getPrimaryDisplay().workAreaSize

    this.window = new BrowserWindow({
      width: 1280,
      height: 768,
      x: Math.round((width - 1280) / 2),
      y: Math.round((height - 768) / 2),
      resizable: true,
      minimizable: true,
      maximizable: true,
      alwaysOnTop: false,
      skipTaskbar: false,
      frame: true,
      title: 'SpeechPilot 設置',
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false
      }
    })

    // 載入 HTML 內容
    console.log('🪟 Loading settings HTML content...')
    this.window.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(this.getSettingsHTML())}`)
      .then(() => {
        console.log('🪟 Settings HTML loaded successfully')
      })
      .catch((error) => {
        console.error('❌ Failed to load settings HTML:', error)
      })

    this.window.on('closed', () => {
      this.window = null
    })

    // 設置 IPC 處理
    this.setupIPC()
  }

  private setupIPC() {
    if (!this.store) {
      console.error('❌ Store not initialized, cannot setup IPC')
      return
    }

    // 移除現有的處理程序（如果存在）
    ipcMain.removeHandler('get-settings')
    ipcMain.removeHandler('save-settings')

    // 獲取設置
    ipcMain.handle('get-settings', () => {
      if (!this.store) return null
      return {
        audioDevice: this.store.get('audioDevice'),
        hotkeys: this.store.get('hotkeys'),
        volume: this.store.get('volume'),
        autoStart: this.store.get('autoStart'),
        languages: this.store.get('languages'),
        recordingTimeout: this.store.get('recordingTimeout'),
        hotkeyMode: this.store.get('hotkeyMode')
      }
    })

    // 保存設置
    ipcMain.handle('save-settings', (_event: any, settings: SettingsData) => {
      if (!this.store) return false
      Object.keys(settings).forEach(key => {
        this.store.set(key as keyof SettingsData, settings[key as keyof SettingsData])
      })
      console.log('💾 Settings saved:', settings)

      // 發送設定變更事件給主進程
      const { ipcMain } = require('electron')
      ipcMain.emit('settings-changed', settings)

      return true
    })

    // 獲取音頻設備列表
    ipcMain.handle('get-audio-devices', async () => {
      try {
        const { AudioService } = require('../services/AudioService')
        const audioService = new AudioService()
        const devices = await audioService.getAudioDevices()
        return devices
      } catch (error) {
        console.error('❌ Failed to get audio devices:', error)
        return []
      }
    })

    // 設置自動啟動
    ipcMain.handle('set-auto-start', async (_event: any, enabled: boolean) => {
      try {
        const { app } = require('electron')
        app.setLoginItemSettings({
          openAtLogin: enabled,
          path: process.execPath
        })
        console.log(`💾 Auto-start ${enabled ? 'enabled' : 'disabled'}`)
        return true
      } catch (error) {
        console.error('❌ Failed to set auto-start:', error)
        return false
      }
    })

    // 獲取自動啟動狀態
    ipcMain.handle('get-auto-start', () => {
      try {
        const { app } = require('electron')
        return app.getLoginItemSettings().openAtLogin
      } catch (error) {
        console.error('❌ Failed to get auto-start status:', error)
        return false
      }
    })
  }

  // 獲取設置數據
  getSettings(): SettingsData {
    return {
      audioDevice: this.store.get('audioDevice'),
      hotkeys: this.store.get('hotkeys'),
      volume: this.store.get('volume'),
      autoStart: this.store.get('autoStart'),
      languages: this.store.get('languages'),
      recordingTimeout: this.store.get('recordingTimeout'),
      hotkeyMode: this.store.get('hotkeyMode')
    }
  }

  // 保存設置數據
  saveSettings(settings: Partial<SettingsData>) {
    Object.keys(settings).forEach(key => {
      this.store.set(key as keyof SettingsData, settings[key as keyof SettingsData])
    })
  }

  private getSettingsHTML(): string {
    return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>SpeechPilot 設置</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 10px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      min-height: 100vh;
      box-sizing: border-box;
      overflow: hidden;
    }

    .container {
      max-width: 1100px;
      margin: 0 auto;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border-radius: 12px;
      padding: 12px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      height: calc(100vh - 20px);
      overflow: hidden;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
    }

    h1 {
      text-align: center;
      margin-bottom: 12px;
      font-size: 18px;
      font-weight: 300;
      flex-shrink: 0;
    }

    .settings-content {
      flex: 1;
      overflow-y: auto;
      padding-right: 5px;
    }

    .setting-group {
      margin-bottom: 10px;
      padding: 10px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .setting-label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
      font-size: 12px;
    }

    select, input {
      width: 100%;
      padding: 6px;
      border: none;
      border-radius: 6px;
      background: rgba(255, 255, 255, 0.9);
      color: #333;
      font-size: 12px;
      box-sizing: border-box;
    }

    select:focus, input:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
    }

    .hotkey-group {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;
    }

    .save-button {
      width: 200px;
      padding: 10px;
      background: linear-gradient(45deg, #4CAF50, #45a049);
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      margin: 15px auto 0;
      display: block;
    }

    .save-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
    }

    .loading {
      text-align: center;
      padding: 20px;
      opacity: 0.7;
    }

    .status {
      text-align: center;
      padding: 10px;
      margin-top: 10px;
      border-radius: 10px;
      font-weight: 500;
    }

    .status.success {
      background: rgba(76, 175, 80, 0.3);
      color: #4CAF50;
    }

    .status.error {
      background: rgba(244, 67, 54, 0.3);
      color: #f44336;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🎙️ SpeechPilot 設置</h1>

    <div class="settings-content">
    <div class="setting-group">
      <label class="setting-label">音頻輸入設備</label>
      <select id="audioDevice">
        <option value="">載入中...</option>
      </select>
    </div>

    <div class="setting-group">
      <label class="setting-label">快捷鍵設置</label>
      <div class="hotkey-group">
        <div>
          <label class="setting-label">AI 模式</label>
          <input type="text" id="aiHotkey" placeholder="Ctrl+Shift+A" readonly>
        </div>
        <div>
          <label class="setting-label">直接轉錄</label>
          <input type="text" id="directHotkey" placeholder="Ctrl+Shift+V" readonly>
        </div>
      </div>
    </div>

    <div class="setting-group">
      <label class="setting-label">音量檢測閾值 (0-100)</label>
      <input type="range" id="volumeThreshold" min="0" max="100" value="10">
      <div style="text-align: center; margin-top: 5px;">
        <span id="volumeValue">10</span>
      </div>
    </div>

    <div class="setting-group">
      <label class="setting-label">系統啟動設置</label>
      <div style="display: flex; align-items: center; gap: 10px;">
        <input type="checkbox" id="autoStart" style="transform: scale(1.2);">
        <label for="autoStart" style="margin: 0;">開機時自動啟動 SpeechPilot</label>
      </div>
    </div>

    <div class="setting-group">
      <label class="setting-label">快捷鍵模式</label>
      <select id="hotkeyMode">
        <option value="toggle">切換模式 (按一次開始，再按一次停止)</option>
        <option value="hold">按住模式 (按住錄音，放開停止)</option>
      </select>
    </div>

    <div class="setting-group">
      <label class="setting-label">常用語言選擇</label>
      <div id="languageSelection" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 8px; margin-top: 8px;">
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Chinese" class="language-checkbox">
          <span>Chinese</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="English" class="language-checkbox">
          <span>English</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Spanish" class="language-checkbox">
          <span>Spanish</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="French" class="language-checkbox">
          <span>French</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="German" class="language-checkbox">
          <span>German</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Japanese" class="language-checkbox">
          <span>Japanese</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Korean" class="language-checkbox">
          <span>Korean</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Portuguese" class="language-checkbox">
          <span>Portuguese</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Russian" class="language-checkbox">
          <span>Russian</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Arabic" class="language-checkbox">
          <span>Arabic</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Hindi" class="language-checkbox">
          <span>Hindi</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Italian" class="language-checkbox">
          <span>Italian</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Dutch" class="language-checkbox">
          <span>Dutch</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Turkish" class="language-checkbox">
          <span>Turkish</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Polish" class="language-checkbox">
          <span>Polish</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Swedish" class="language-checkbox">
          <span>Swedish</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Norwegian" class="language-checkbox">
          <span>Norwegian</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Danish" class="language-checkbox">
          <span>Danish</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Finnish" class="language-checkbox">
          <span>Finnish</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Greek" class="language-checkbox">
          <span>Greek</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Hebrew" class="language-checkbox">
          <span>Hebrew</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Thai" class="language-checkbox">
          <span>Thai</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Vietnamese" class="language-checkbox">
          <span>Vietnamese</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Indonesian" class="language-checkbox">
          <span>Indonesian</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Malay" class="language-checkbox">
          <span>Malay</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Filipino" class="language-checkbox">
          <span>Filipino</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Czech" class="language-checkbox">
          <span>Czech</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Hungarian" class="language-checkbox">
          <span>Hungarian</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Romanian" class="language-checkbox">
          <span>Romanian</span>
        </label>
        <label style="display: flex; align-items: center; gap: 6px; font-size: 11px;">
          <input type="checkbox" value="Bulgarian" class="language-checkbox">
          <span>Bulgarian</span>
        </label>
      </div>
    </div>

    <div class="setting-group">
      <label class="setting-label">錄音超時設定 (秒)</label>
      <input type="number" id="recordingTimeout" min="1" max="60" value="5" style="width: 100px;">
      <div style="margin-top: 5px; font-size: 12px; opacity: 0.8;">
        AI 模式下靜音超過此時間將自動停止錄音
      </div>
    </div>
    </div>

    <button class="save-button" onclick="saveSettings()">💾 保存設置</button>

    <div id="status" class="status" style="display: none;"></div>
  </div>

  <script>
    const { ipcRenderer } = require('electron')
    
    let currentSettings = {}

    // 載入設置
    async function loadSettings() {
      try {
        currentSettings = await ipcRenderer.invoke('get-settings')
        
        // 載入音頻設備
        const devices = await ipcRenderer.invoke('get-audio-devices')
        const deviceSelect = document.getElementById('audioDevice')
        deviceSelect.innerHTML = ''
        
        // 添加默認選項
        const defaultOption = document.createElement('option')
        defaultOption.value = 'default'
        defaultOption.textContent = '系統默認設備'
        deviceSelect.appendChild(defaultOption)
        
        // 添加設備選項
        devices.forEach(device => {
          const option = document.createElement('option')
          option.value = device.name
          option.textContent = device.name
          deviceSelect.appendChild(option)
        })
        
        // 設置當前值
        deviceSelect.value = currentSettings.audioDevice || 'default'
        document.getElementById('aiHotkey').value = currentSettings.hotkeys?.ai || 'CommandOrControl+Shift+A'
        document.getElementById('directHotkey').value = currentSettings.hotkeys?.direct || 'CommandOrControl+Shift+V'
        document.getElementById('volumeThreshold').value = currentSettings.volume?.threshold || 10
        document.getElementById('volumeValue').textContent = currentSettings.volume?.threshold || 10

        // 載入自動啟動狀態
        const autoStartStatus = await ipcRenderer.invoke('get-auto-start')
        document.getElementById('autoStart').checked = autoStartStatus || currentSettings.autoStart || false

        // 設置快捷鍵模式
        document.getElementById('hotkeyMode').value = currentSettings.hotkeyMode || 'toggle'

        // 設置語言選擇
        const selectedLanguages = currentSettings.languages || ['Chinese', 'English']
        const languageCheckboxes = document.querySelectorAll('.language-checkbox')
        languageCheckboxes.forEach(checkbox => {
          checkbox.checked = selectedLanguages.includes(checkbox.value)
        })

        // 設置錄音超時
        document.getElementById('recordingTimeout').value = currentSettings.recordingTimeout || 5
        
      } catch (error) {
        console.error('Failed to load settings:', error)
        showStatus('載入設置失敗', 'error')
      }
    }

    // 保存設置
    async function saveSettings() {
      try {
        // 收集選中的語言
        const selectedLanguages = []
        const languageCheckboxes = document.querySelectorAll('.language-checkbox')
        languageCheckboxes.forEach(checkbox => {
          if (checkbox.checked) {
            selectedLanguages.push(checkbox.value)
          }
        })

        const settings = {
          audioDevice: document.getElementById('audioDevice').value,
          hotkeys: {
            ai: document.getElementById('aiHotkey').value,
            direct: document.getElementById('directHotkey').value
          },
          volume: {
            threshold: parseInt(document.getElementById('volumeThreshold').value)
          },
          autoStart: document.getElementById('autoStart').checked,
          languages: selectedLanguages,
          recordingTimeout: parseInt(document.getElementById('recordingTimeout').value),
          hotkeyMode: document.getElementById('hotkeyMode').value
        }

        // 保存自動啟動設置
        await ipcRenderer.invoke('set-auto-start', settings.autoStart)

        await ipcRenderer.invoke('save-settings', settings)
        showStatus('設置已保存', 'success')

      } catch (error) {
        console.error('Failed to save settings:', error)
        showStatus('保存設置失敗', 'error')
      }
    }

    // 顯示狀態消息
    function showStatus(message, type) {
      const status = document.getElementById('status')
      status.textContent = message
      status.className = 'status ' + type
      status.style.display = 'block'
      
      setTimeout(() => {
        status.style.display = 'none'
      }, 3000)
    }

    // 音量滑塊事件
    document.getElementById('volumeThreshold').addEventListener('input', function() {
      document.getElementById('volumeValue').textContent = this.value
    })

    // 載入設置
    loadSettings()
  </script>
</body>
</html>
    `
  }
}
