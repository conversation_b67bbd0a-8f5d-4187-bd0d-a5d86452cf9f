import { BrowserWindow } from 'electron'
import { httpsCallable } from 'firebase/functions'
import {
  signInWithGoogle,
  signInWithFacebook,
  signInWithApple,
  signOutUser,
  getCurrentUserToken,

  onAuthStateChange,
  waitForAuthInit,
  FirebaseUser,
  functions
} from '../../../firebase'
import { FirebaseFunctionLogger } from '../utils/FirebaseFunctionLogger'

export interface AuthState {
  isAuthenticated: boolean
  user: FirebaseUser | null
  token: string | null
  loading: boolean
}

export interface UserInfo {
  user: {
    id: string
    email: string
    name?: string
    image?: string
    subscriptionPlan: string
    subscriptionStatus?: string
    isActive: boolean
    isBanned: boolean
  }
  usage: {
    totalSeconds: number
    dailySeconds: number
    dailyLimit: number
    remainingSeconds: number
    canUse: boolean
  }
  devices: any[]
  recentUsage: any[]
}

export class AuthService {
  private authWindow: BrowserWindow | null = null
  private authState: AuthState = {
    isAuthenticated: false,
    user: null,
    token: null,
    loading: true
  }
  private listeners: ((state: AuthState) => void)[] = []

  constructor() {
    this.initializeAuth()
  }

  private async initializeAuth() {
    try {
      // 等待 Firebase Auth 初始化
      const user = await waitForAuthInit()
      
      if (user) {
        const token = await getCurrentUserToken()
        this.updateAuthState({
          isAuthenticated: true,
          user,
          token,
          loading: false
        })
      } else {
        this.updateAuthState({
          isAuthenticated: false,
          user: null,
          token: null,
          loading: false
        })
      }

      // 監聽認證狀態變化
      onAuthStateChange(async (user) => {
        if (user) {
          const token = await getCurrentUserToken()
          this.updateAuthState({
            isAuthenticated: true,
            user,
            token,
            loading: false
          })
        } else {
          this.updateAuthState({
            isAuthenticated: false,
            user: null,
            token: null,
            loading: false
          })
        }
      })

    } catch (error) {
      console.error('認證初始化失敗:', error)
      this.updateAuthState({
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false
      })
    }
  }

  private updateAuthState(newState: Partial<AuthState>) {
    this.authState = { ...this.authState, ...newState }
    this.notifyListeners()
  }

  private notifyListeners() {
    this.listeners.forEach(listener => listener(this.authState))
  }

  // 添加認證狀態監聽器
  onAuthStateChanged(listener: (state: AuthState) => void) {
    this.listeners.push(listener)
    // 立即調用一次以獲取當前狀態
    listener(this.authState)
    
    // 返回取消監聽的函數
    return () => {
      const index = this.listeners.indexOf(listener)
      if (index > -1) {
        this.listeners.splice(index, 1)
      }
    }
  }

  // Google 登入
  async signInWithGoogle(): Promise<boolean> {
    try {
      this.updateAuthState({ loading: true })

      const user = await signInWithGoogle()
      if (user) {
        await this.handleSuccessfulAuth(user)
        return true
      }
      return false
    } catch (error) {
      console.error('Google 登入失敗:', error)
      this.updateAuthState({ loading: false })
      throw error
    }
  }

  // Facebook 登入
  async signInWithFacebook(): Promise<boolean> {
    try {
      this.updateAuthState({ loading: true })

      const user = await signInWithFacebook()
      if (user) {
        await this.handleSuccessfulAuth(user)
        return true
      }
      return false
    } catch (error) {
      console.error('Facebook 登入失敗:', error)
      this.updateAuthState({ loading: false })
      throw error
    }
  }

  // Apple 登入
  async signInWithApple(): Promise<boolean> {
    try {
      this.updateAuthState({ loading: true })

      const user = await signInWithApple()
      if (user) {
        await this.handleSuccessfulAuth(user)
        return true
      }
      return false
    } catch (error) {
      console.error('Apple 登入失敗:', error)
      this.updateAuthState({ loading: false })
      throw error
    }
  }

  // 處理成功認證
  private async handleSuccessfulAuth(user: FirebaseUser) {
    try {
      // 更新本地認證狀態
      this.updateAuthState({
        isAuthenticated: true,
        user,
        token: await user.getIdToken(),
        loading: false
      })

      // 創建或更新用戶記錄
      await this.createOrUpdateUser(user)

      console.log('用戶認證成功:', user.email)
    } catch (error) {
      console.error('處理認證失敗:', error)
      this.updateAuthState({ loading: false })
      throw error
    }
  }

  // 創建或更新用戶記錄
  private async createOrUpdateUser(user: FirebaseUser) {
    try {
      const provider = user.providerData[0]?.providerId || 'unknown'
      const authProvider = provider.includes('google') ? 'google' :
                          provider.includes('facebook') ? 'facebook' :
                          provider.includes('apple') ? 'apple' : 'email'

      const userData = {
        uid: user.uid,
        email: user.email,
        name: user.displayName,
        image: user.photoURL,
        authProvider,
        emailVerified: user.emailVerified,
        platform: FirebaseFunctionLogger.normalizePlatform(process.platform), // 標準化平台名稱
        appVersion: require('electron').app.getVersion()
      }

      console.log('📤 準備創建/更新用戶記錄:', userData)

      // 使用增強的日誌包裝器調用 Firebase Function (修正函數名稱)
      const result = await FirebaseFunctionLogger.call('create_or_update_user', userData)

      // 檢查是否為新用戶
      if (result.data?.user?.isNewUser) {
        console.log('🆕 新用戶註冊完成，已自動分配 Free 計劃')
      } else {
        console.log('👤 現有用戶資料已更新')
      }

      return result.data

    } catch (error) {
      console.error('❌ 創建用戶記錄失敗')
      // 不拋出錯誤，允許用戶繼續使用（可能是網路問題）
      console.log('⚠️ 繼續使用本地認證狀態')
      return null
    }
  }

  // 登出
  async signOut(): Promise<void> {
    try {
      await signOutUser()
      this.updateAuthState({
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false
      })
      console.log('用戶已登出')
    } catch (error) {
      console.error('登出失敗:', error)
      throw error
    }
  }

  // 獲取當前認證狀態
  getAuthState(): AuthState {
    return { ...this.authState }
  }

  // 獲取當前用戶
  getCurrentUser(): FirebaseUser | null {
    return this.authState.user
  }

  // 獲取當前用戶 Token
  async getToken(): Promise<string | null> {
    try {
      const token = await getCurrentUserToken()
      this.updateAuthState({ token })
      return token
    } catch (error) {
      console.error('獲取 Token 失敗:', error)
      return null
    }
  }

  // 獲取用戶資訊
  async getUserInfo(): Promise<UserInfo | null> {
    try {
      if (!this.authState.isAuthenticated) {
        throw new Error('用戶未認證')
      }

      // 使用增強的日誌包裝器調用 Firebase Function
      const result = await FirebaseFunctionLogger.call('getUserInfo')
      
      return result.data as UserInfo
    } catch (error) {
      console.error('獲取用戶資訊失敗:', error)
      throw error
    }
  }

  // 檢查是否已認證
  isAuthenticated(): boolean {
    return this.authState.isAuthenticated
  }

  // 檢查是否正在載入
  isLoading(): boolean {
    return this.authState.loading
  }

  // 工作流程 1: 檢查用戶訂閱狀態
  async checkSubscriptionStatus(): Promise<{
    canUse: boolean
    availableSecondsToday: number
    availableSecondsThisMonth: number
    needsUpgrade: boolean
    upgradeUrl?: string
    plan: string
    planName?: string
    maxDevices?: number
    deviceCount?: number
  } | null> {
    try {
      if (!this.authState.isAuthenticated) {
        throw new Error('用戶未認證')
      }

      // 使用增強的日誌包裝器調用 Firebase Function
      const result = await FirebaseFunctionLogger.call('check_user_subscription_status')

      // 根據實際的 API 回應結構解析數據
      const data = result.data
      console.log(`📋 當前計劃: ${data.plan}`)
      console.log(`⏰ 今日剩餘: ${data.available_seconds_today} 秒`)
      console.log(`📱 用戶狀態: ${data.is_active ? '活躍' : '非活躍'}`)

      return {
        canUse: data.can_use,
        availableSecondsToday: data.available_seconds_today,
        availableSecondsThisMonth: data.available_seconds_this_month,
        needsUpgrade: data.needs_upgrade,
        upgradeUrl: data.upgrade_url,
        plan: data.plan,
        planName: data.plan, // 使用 plan 作為 planName
        maxDevices: 1 // FREE 計劃預設為 1 台設備
      }

      return null

    } catch (error) {
      console.error('檢查訂閱狀態失敗:', error)
      return null
    }
  }

  // 工作流程 2: 錄音前檢查可用時間
  async checkUsageBeforeRecording(estimatedDurationSeconds: number = 60): Promise<{
    canRecord: boolean
    availableSeconds: number
    reason?: string
    upgradeRequired?: boolean
    upgradeUrl?: string
  } | null> {
    try {
      if (!this.authState.isAuthenticated) {
        throw new Error('用戶未認證')
      }

      const checkUsage = httpsCallable(functions, 'checkUsageBeforeRecording')
      const result = await checkUsage({ estimatedDurationSeconds })

      return result.data as any

    } catch (error) {
      console.error('檢查錄音前可用時間失敗:', error)
      return null
    }
  }

  // 工作流程 3: 錄音完成後計算使用量
  async calculateUsageAfterRecording(data: {
    deviceId: string
    sessionStart: Date
    sessionEnd: Date
    features: string[]
    platform: string
    appVersion?: string
  }): Promise<{
    secondsUsed: number
    tokensUsed?: number
    success: boolean
    error?: string
  } | null> {
    try {
      if (!this.authState.isAuthenticated) {
        throw new Error('用戶未認證')
      }

      const calculateUsage = httpsCallable(functions, 'calculateUsageAfterRecording')
      const result = await calculateUsage({
        deviceId: data.deviceId,
        sessionStart: data.sessionStart.toISOString(),
        sessionEnd: data.sessionEnd.toISOString(),
        features: data.features,
        platform: data.platform,
        appVersion: data.appVersion
      })

      return result.data as any

    } catch (error) {
      console.error('計算使用量失敗:', error)
      return null
    }
  }

  // 清理資源
  destroy() {
    if (this.authWindow) {
      this.authWindow.destroy()
      this.authWindow = null
    }
    this.listeners = []
  }
}
