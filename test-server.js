// 測試用 Firebase Functions 模擬服務器
const http = require('http')
const url = require('url')
const { exec } = require('child_process')

// 創建 HTTP 服務器
const server = http.createServer((req, res) => {
  // 設置 CORS 標頭
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type')

  if (req.method === 'OPTIONS') {
    res.writeHead(200)
    res.end()
    return
  }

  const parsedUrl = url.parse(req.url, true)
  const pathname = parsedUrl.pathname
  const query = parsedUrl.query

  // 處理 OAuth 請求
  if (pathname.startsWith('/auth/')) {
    const provider = pathname.split('/')[2] // google, facebook, apple
    const { state, platform } = query
    console.log(`🔐 ${provider} OAuth requested:`, { state, platform })

    // 模擬 OAuth 流程
    setTimeout(() => {
      const mockToken = generateMockCustomToken(provider)
      const callbackUrl = `speechpilot://oauth/callback?token=${mockToken}&state=${state}`

      console.log('✅ Redirecting to:', callbackUrl)

      // 觸發 deep link
      if (platform === 'desktop') {
        if (process.platform === 'win32') {
          exec(`start "" "${callbackUrl}"`, (error) => {
            if (error) console.error('Failed to trigger deep link:', error)
          })
        } else if (process.platform === 'darwin') {
          exec(`open "${callbackUrl}"`, (error) => {
            if (error) console.error('Failed to trigger deep link:', error)
          })
        }
      }

      // 返回成功頁面
      res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' })
      res.end(`
        <html>
          <head>
            <title>SpeechPilot OAuth 測試</title>
            <meta charset="utf-8">
            <style>
              body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }
              .container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
              .success { color: #4CAF50; }
              .info { color: #2196F3; }
              .warning { color: #FF9800; background: #FFF3E0; padding: 15px; border-radius: 5px; margin: 20px 0; }
              .button { background: #2196F3; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; margin: 10px; }
              .button:hover { background: #1976D2; }
              .deep-link { background: #f0f0f0; padding: 10px; border-radius: 5px; word-break: break-all; margin: 10px 0; }
            </style>
          </head>
          <body>
            <div class="container">
              <h1 class="success">✅ ${provider.charAt(0).toUpperCase() + provider.slice(1)} 測試登入成功</h1>

              <div class="warning">
                <strong>⚠️ 這是測試模式</strong><br>
                這不是真正的 ${provider.charAt(0).toUpperCase() + provider.slice(1)} OAuth，只是模擬流程。<br>
                在生產環境中，這裡會是真正的 ${provider.charAt(0).toUpperCase() + provider.slice(1)} 登入頁面。
              </div>

              <p class="info">正在嘗試自動重定向到 SpeechPilot 應用程式...</p>

              <div class="deep-link">
                Deep Link: <code>${callbackUrl}</code>
              </div>

              <button class="button" onclick="window.location.href='${callbackUrl}'">
                🔗 手動觸發 Deep Link
              </button>

              <button class="button" onclick="copyToClipboard('${callbackUrl}')">
                📋 複製 Deep Link
              </button>

              <p><small>如果 Deep Link 無法工作，請檢查 URL scheme 是否正確註冊。</small></p>
            </div>

            <script>
              function copyToClipboard(text) {
                navigator.clipboard.writeText(text).then(() => {
                  alert('Deep Link 已複製到剪貼板')
                })
              }

              // 自動重定向
              setTimeout(() => {
                console.log('Attempting automatic redirect to:', '${callbackUrl}')
                window.location.href = '${callbackUrl}'
              }, 3000)
            </script>
          </body>
        </html>
      `)
    }, 1000)

    return
  }

  // 健康檢查
  if (pathname === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' })
    res.end(JSON.stringify({ status: 'ok', message: 'SpeechPilot Test Server is running' }))
    return
  }

  // 404
  res.writeHead(404, { 'Content-Type': 'text/plain' })
  res.end('Not Found')
})


// 生成模擬的 Firebase Custom Token
function generateMockCustomToken(provider) {
  const header = Buffer.from(JSON.stringify({
    alg: 'RS256',
    typ: 'JWT'
  })).toString('base64url')
  
  const payload = Buffer.from(JSON.stringify({
    iss: '<EMAIL>',
    sub: `${provider}-user-${Date.now()}`,
    aud: 'https://identitytoolkit.googleapis.com/google.identity.identitytoolkit.v1.IdentityToolkit',
    exp: Math.floor(Date.now() / 1000) + 3600,
    iat: Math.floor(Date.now() / 1000),
    uid: `${provider}-user-${Date.now()}`,
    claims: {
      email: `user@${provider === 'google' ? 'gmail.com' : provider === 'apple' ? 'icloud.com' : 'facebook.com'}`,
      name: `${provider.charAt(0).toUpperCase() + provider.slice(1)} User`,
      provider: provider
    }
  })).toString('base64url')
  
  const signature = 'mock-signature'
  
  return `${header}.${payload}.${signature}`
}

// 啟動服務器
const PORT = process.env.PORT || 3000
server.listen(PORT, () => {
  console.log(`🚀 SpeechPilot Test Server running on http://localhost:${PORT}`)
  console.log('📋 Available endpoints:')
  console.log('  GET /auth/google?state=xxx&platform=desktop')
  console.log('  GET /auth/facebook?state=xxx&platform=desktop')
  console.log('  GET /auth/apple?state=xxx&platform=desktop')
  console.log('  GET /health')
})
