# 🧪 測試用 Firebase Functions

由於完整的 Firebase Functions 需要後端開發，這裡提供一個測試方案來驗證 SSO 流程。

## 方案 1: 本地測試服務器

創建一個簡單的本地服務器來模擬 Firebase Functions：

```javascript
// test-server.js
const express = require('express')
const cors = require('cors')
const app = express()

app.use(cors())
app.use(express.json())

// 模擬 Google OAuth
app.get('/auth/google', (req, res) => {
  const { state } = req.query
  
  // 模擬 OAuth 流程，直接返回成功
  setTimeout(() => {
    const mockToken = 'mock-firebase-custom-token-' + Date.now()
    const callbackUrl = `speechpilot://oauth/callback?token=${mockToken}&state=${state}`
    
    // 在真實環境中，這會是瀏覽器重定向
    console.log('Redirect to:', callbackUrl)
    res.send(`
      <html>
        <body>
          <h1>OAuth 成功</h1>
          <p>正在重定向到應用程式...</p>
          <script>
            setTimeout(() => {
              window.location.href = '${callbackUrl}'
            }, 2000)
          </script>
        </body>
      </html>
    `)
  }, 1000)
})

// 模擬其他 OAuth 提供商
app.get('/auth/facebook', (req, res) => {
  // 同 Google 邏輯
})

app.get('/auth/apple', (req, res) => {
  // 同 Google 邏輯
})

app.listen(3000, () => {
  console.log('Test server running on http://localhost:3000')
})
```

## 方案 2: 修改客戶端進行測試

暫時修改客戶端代碼來模擬成功的 OAuth 流程：

```typescript
// 在 firebase.ts 中添加測試模式
const openExternalOAuth = async (provider: 'google' | 'facebook' | 'apple'): Promise<string> => {
  if (process.env.NODE_ENV === 'development') {
    // 測試模式：模擬成功的 OAuth
    console.log(`🧪 Testing ${provider} OAuth flow...`)
    
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockToken = `mock-${provider}-token-${Date.now()}`
        resolve(mockToken)
      }, 2000) // 模擬 2 秒的 OAuth 流程
    })
  }
  
  // 生產模式的實際實現...
}
```

## 方案 3: 使用 Firebase 模擬器

如果你有 Firebase CLI，可以使用模擬器：

```bash
# 安裝 Firebase CLI
npm install -g firebase-tools

# 初始化 Firebase 項目
firebase init functions

# 啟動模擬器
firebase emulators:start --only functions
```

## 建議的測試流程

1. **階段 1**: 使用方案 2 測試客戶端 UI 和流程
2. **階段 2**: 實現簡單的本地測試服務器（方案 1）
3. **階段 3**: 部署真實的 Firebase Functions

## 當前狀態

目前的實現已經包含了：
- ✅ 完整的客戶端 SSO UI
- ✅ Deep link 處理機制
- ✅ 外部瀏覽器 OAuth 流程
- ✅ Firebase Custom Token 處理
- ⏳ 需要後端 Firebase Functions

要測試當前實現，你需要：
1. 設置 Firebase 項目和 Functions
2. 或者使用上述測試方案之一
3. 配置正確的環境變數

這樣可以在沒有完整後端的情況下測試整個認證流程。
