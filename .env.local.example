# SpeechPilot 環境變數配置
# 複製此文件為 .env.local 並填入您的實際值

# ================================
# Firebase 配置
# ================================
# 從 Firebase Console > Project Settings > General > Your apps 獲取
FIREBASE_API_KEY=your-firebase-api-key
FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_STORAGE_BUCKET=your-project.appspot.com
FIREBASE_MESSAGING_SENDER_ID=123456789
FIREBASE_APP_ID=1:123456789:web:abcdef123456

# ================================
# Firebase Functions URL
# ================================
# 部署後的 Firebase Functions 基礎 URL
# 格式: https://your-region-your-project-id.cloudfunctions.net
FIREBASE_FUNCTIONS_URL=https://us-central1-your-project-id.cloudfunctions.net

# ================================
# Web Portal 配置
# ================================
# SpeechPilot Web Portal 的 URL（用於升級連結）
WEB_PORTAL_URL=https://your-speechpilot-web-portal.com

# ================================
# 開發環境配置
# ================================
# 是否使用 Firebase 模擬器（開發時使用）
USE_FIREBASE_EMULATOR=false

# 開發模式
NODE_ENV=production

# ================================
# 應用程式配置
# ================================
# 應用程式版本
APP_VERSION=1.0.0

# 日誌級別 (error, warn, info, debug)
LOG_LEVEL=info

# ================================
# 安全配置
# ================================
# 設備註冊密鑰（可選，用於額外安全驗證）
DEVICE_REGISTRATION_KEY=your-device-registration-key

# 客戶端加密密鑰（用於本地數據加密）
CLIENT_ENCRYPTION_KEY=your-client-encryption-key

# ================================
# 功能開關
# ================================
# 是否啟用使用量追蹤
ENABLE_USAGE_TRACKING=true

# 是否啟用設備註冊
ENABLE_DEVICE_REGISTRATION=true

# 是否啟用離線模式
ENABLE_OFFLINE_MODE=false

# ================================
# 調試配置
# ================================
# 是否啟用詳細日誌
ENABLE_VERBOSE_LOGGING=false

# 是否啟用效能監控
ENABLE_PERFORMANCE_MONITORING=true
