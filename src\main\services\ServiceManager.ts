import { AuthService } from './AuthService'
import { UsageTrackingService } from './UsageTrackingService'
import { app } from 'electron'

export interface ServiceManagerEvents {
  'auth-state-changed': (isAuthenticated: boolean) => void
  'usage-session-started': () => void
  'usage-session-ended': () => void
  'usage-limit-exceeded': (remainingSeconds: number) => void
  'device-registration-failed': (error: string) => void
  'device-validation-failed': (error: string) => void
  'subscription-check-failed': (error: string) => void
  'session-initialization-failed': (error: string) => void
}

export class ServiceManager {
  private authService: AuthService
  private usageTrackingService: UsageTrackingService
  private eventListeners: Map<keyof ServiceManagerEvents, Function[]> = new Map()
  private isInitialized = false

  constructor() {
    this.authService = new AuthService()
    this.usageTrackingService = new UsageTrackingService(this.authService)
    this.setupEventListeners()
  }

  private setupEventListeners() {
    // 監聽認證狀態變化
    this.authService.onAuthStateChanged((authState) => {
      this.emit('auth-state-changed', authState.isAuthenticated)
      
      if (!authState.isAuthenticated) {
        // 用戶登出時結束當前會話
        this.usageTrackingService.endSession()
      }
    })

    // 監聽應用程式關閉事件
    app.on('before-quit', async () => {
      await this.cleanup()
    })

    app.on('window-all-closed', async () => {
      await this.cleanup()
    })
  }

  // 初始化服務
  async initialize(): Promise<void> {
    try {
      if (this.isInitialized) {
        return
      }

      console.log('正在初始化服務管理器...')
      
      // 等待認證服務初始化
      // AuthService 會在構造函數中自動初始化
      
      this.isInitialized = true
      console.log('服務管理器初始化完成')
      
    } catch (error) {
      console.error('服務管理器初始化失敗:', error)
      throw error
    }
  }

  // 獲取認證服務
  getAuthService(): AuthService {
    return this.authService
  }

  // 獲取使用量追蹤服務
  getUsageTrackingService(): UsageTrackingService {
    return this.usageTrackingService
  }

  // Google 登入 - 完整工作流程
  async signInWithGoogle(): Promise<boolean> {
    try {
      console.log('🔐 開始 Google 登入工作流程...')

      // 1. Google OAuth 登入
      const success = await this.authService.signInWithGoogle()

      if (success) {
        console.log('✅ Google 登入成功')

        // 2. 執行登入後初始化流程
        await this.initializeUserSession()

        return true
      }

      return false
    } catch (error) {
      console.error('Google 登入失敗:', error)
      return false
    }
  }

  // 登入後初始化用戶會話 - 修正工作流程順序
  private async initializeUserSession(): Promise<void> {
    try {
      console.log('🔄 初始化用戶會話...')

      // 根據 API_OVERVIEW.md 的正確工作流程：

      // 1. 檢查訂閱狀態（create_or_update_user 已在 AuthService 中調用）
      console.log('📋 檢查用戶訂閱狀態...')
      const subscriptionStatus = await this.authService.checkSubscriptionStatus()

      if (subscriptionStatus) {
        console.log(`✅ 訂閱狀態檢查成功: ${subscriptionStatus.planName}`)
        console.log(`⏰ 今日剩餘: ${subscriptionStatus.availableSecondsToday} 秒`)
        console.log(`📱 設備限制: ${subscriptionStatus.maxDevices} 台`)

        // 2. 註冊設備（在確認訂閱狀態後）
        console.log('📱 註冊設備...')
        const deviceRegistered = await this.usageTrackingService.registerDevice()

        if (deviceRegistered) {
          console.log('✅ 設備註冊成功')

          // 3. 驗證設備存取權限
          console.log('🔍 驗證設備存取權限...')
          const hasAccess = await this.usageTrackingService.validateDevice()

          if (hasAccess) {
            console.log('✅ 設備驗證成功')

            if (subscriptionStatus.canUse) {
              console.log('🎉 完整登入流程成功！用戶可以開始使用應用')
            } else {
              console.warn('⚠️ 使用時間已用完，需要升級計劃')
            }
          } else {
            console.error('❌ 設備驗證失敗')
            this.emit('device-validation-failed', '設備驗證失敗')
          }
        } else {
          console.error('❌ 設備註冊失敗')
          this.emit('device-registration-failed', '設備註冊失敗')
        }
      } else {
        console.error('❌ 訂閱狀態檢查失敗')
        this.emit('subscription-check-failed', '訂閱狀態檢查失敗')
      }

    } catch (error) {
      console.error('❌ 用戶會話初始化失敗:', error)
      this.emit('session-initialization-failed', error instanceof Error ? error.message : String(error))
      // 不拋出錯誤，允許用戶繼續使用
    }
  }

  // Facebook 登入
  async signInWithFacebook(): Promise<boolean> {
    try {
      return await this.authService.signInWithFacebook()
    } catch (error) {
      console.error('Facebook 登入失敗:', error)
      return false
    }
  }

  // Apple 登入
  async signInWithApple(): Promise<boolean> {
    try {
      return await this.authService.signInWithApple()
    } catch (error) {
      console.error('Apple 登入失敗:', error)
      return false
    }
  }

  // 登出
  async signOut(): Promise<void> {
    try {
      // 先結束使用會話
      await this.usageTrackingService.endSession()
      
      // 然後登出
      await this.authService.signOut()
      
    } catch (error) {
      console.error('登出失敗:', error)
      throw error
    }
  }

  // 工作流程 1: 檢查用戶認證和訂閱狀態
  async checkAuthAndSubscription(): Promise<{
    isAuthenticated: boolean
    canUse: boolean
    availableSecondsToday: number
    availableSecondsThisMonth: number
    needsUpgrade: boolean
    upgradeUrl?: string
    plan?: string
  }> {
    try {
      if (!this.authService.isAuthenticated()) {
        return {
          isAuthenticated: false,
          canUse: false,
          availableSecondsToday: 0,
          availableSecondsThisMonth: 0,
          needsUpgrade: true
        }
      }

      const subscriptionStatus = await this.authService.checkSubscriptionStatus()

      if (!subscriptionStatus) {
        return {
          isAuthenticated: true,
          canUse: false,
          availableSecondsToday: 0,
          availableSecondsThisMonth: 0,
          needsUpgrade: true
        }
      }

      return {
        isAuthenticated: true,
        ...subscriptionStatus
      }

    } catch (error) {
      console.error('檢查認證和訂閱狀態失敗:', error)
      return {
        isAuthenticated: false,
        canUse: false,
        availableSecondsToday: 0,
        availableSecondsThisMonth: 0,
        needsUpgrade: true
      }
    }
  }

  // 工作流程 2: 錄音前檢查
  async checkBeforeRecording(estimatedDurationSeconds: number = 60): Promise<{
    canRecord: boolean
    reason?: string
    upgradeRequired?: boolean
    upgradeUrl?: string
    availableSeconds?: number
  }> {
    try {
      // 先檢查認證狀態
      const authStatus = await this.checkAuthAndSubscription()

      if (!authStatus.isAuthenticated) {
        return {
          canRecord: false,
          reason: '請先登入您的帳戶',
          upgradeRequired: false
        }
      }



      if (!authStatus.canUse) {
        return {
          canRecord: false,
          reason: '使用時間已用完，請升級您的訂閱計劃',
          upgradeRequired: true,
          upgradeUrl: authStatus.upgradeUrl
        }
      }

      // 檢查具體的使用時間
      const usageCheck = await this.authService.checkUsageBeforeRecording(estimatedDurationSeconds)

      if (!usageCheck) {
        return {
          canRecord: false,
          reason: '無法檢查使用時間，請稍後再試',
          upgradeRequired: false
        }
      }

      if (!usageCheck.canRecord) {
        this.emit('usage-limit-exceeded', usageCheck.availableSeconds || 0)
      }

      return usageCheck

    } catch (error) {
      console.error('錄音前檢查失敗:', error)
      return {
        canRecord: false,
        reason: '檢查失敗，請稍後再試',
        upgradeRequired: false
      }
    }
  }

  // 開始使用會話（更新版本）
  async startUsageSession(features: string[] = [], estimatedDurationSeconds: number = 60): Promise<boolean> {
    try {
      // 使用新的工作流程檢查
      const checkResult = await this.checkBeforeRecording(estimatedDurationSeconds)

      if (!checkResult.canRecord) {
        console.log('無法開始錄音:', checkResult.reason)

        if (checkResult.upgradeRequired) {
          this.emit('usage-limit-exceeded', checkResult.availableSeconds || 0)
        }

        return false
      }



      const success = await this.usageTrackingService.startSession(features)
      if (success) {
        this.emit('usage-session-started')
      }

      return success

    } catch (error) {
      console.error('開始使用會話失敗:', error)
      return false
    }
  }

  // 工作流程 3: 結束使用會話並計算使用量
  async endUsageSession(): Promise<{
    success: boolean
    secondsUsed?: number
    tokensUsed?: number
    error?: string
  }> {
    try {
      // 先結束本地會話
      const localSuccess = await this.usageTrackingService.endSession()

      if (!localSuccess) {
        return {
          success: false,
          error: '結束本地會話失敗'
        }
      }

      // 獲取會話資料
      const sessionData = this.usageTrackingService.getLastSessionData()

      if (!sessionData) {
        return {
          success: false,
          error: '無法獲取會話資料'
        }
      }

      // 調用 Firebase Functions 計算使用量
      const calculationResult = await this.authService.calculateUsageAfterRecording({
        deviceId: sessionData.deviceId,
        sessionStart: sessionData.sessionStart,
        sessionEnd: sessionData.sessionEnd,
        features: sessionData.features,
        platform: sessionData.platform,
        appVersion: sessionData.appVersion
      })

      if (!calculationResult || !calculationResult.success) {
        return {
          success: false,
          error: calculationResult?.error || '計算使用量失敗'
        }
      }

      this.emit('usage-session-ended')

      return {
        success: true,
        secondsUsed: calculationResult.secondsUsed,
        tokensUsed: calculationResult.tokensUsed
      }

    } catch (error) {
      console.error('結束使用會話失敗:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知錯誤'
      }
    }
  }

  // 獲取用戶資訊
  async getUserInfo() {
    try {
      return await this.authService.getUserInfo()
    } catch (error) {
      console.error('獲取用戶資訊失敗:', error)
      return null
    }
  }

  // 獲取認證狀態
  getAuthState() {
    return this.authService.getAuthState()
  }

  // 獲取當前使用會話
  getCurrentUsageSession() {
    return this.usageTrackingService.getCurrentSession()
  }

  // 獲取設備資訊
  getDeviceInfo() {
    return this.usageTrackingService.getDeviceInfo()
  }

  // 檢查是否有活動會話
  hasActiveUsageSession(): boolean {
    return this.usageTrackingService.hasActiveSession()
  }

  // 檢查是否已認證
  isAuthenticated(): boolean {
    return this.authService.isAuthenticated()
  }

  // 檢查是否正在載入
  isLoading(): boolean {
    return this.authService.isLoading()
  }

  // 錄音工作流程：開始錄音前檢查
  async startRecordingWorkflow(): Promise<{
    canStart: boolean
    message: string
    availableSeconds?: number
  }> {
    try {
      if (!this.isAuthenticated()) {
        return {
          canStart: false,
          message: '請先登入'
        }
      }

      // 檢查使用量
      const usageCheck = await this.usageTrackingService.checkUsageBeforeRecording()

      if (!usageCheck) {
        return {
          canStart: false,
          message: '無法檢查使用量，請稍後再試'
        }
      }

      if (usageCheck.canStart) {
        // 開始使用會話
        const sessionStarted = await this.usageTrackingService.startSession(['ai-speech-to-text'])

        if (sessionStarted) {
          this.emit('usage-session-started')
          return {
            canStart: true,
            message: '可以開始錄音',
            availableSeconds: usageCheck.availableSeconds
          }
        } else {
          return {
            canStart: false,
            message: '無法開始使用會話'
          }
        }
      } else {
        return {
          canStart: false,
          message: usageCheck.message || '使用時間已用完，請升級您的訂閱計劃'
        }
      }

    } catch (error) {
      console.error('❌ 錄音工作流程錯誤:', error)
      return {
        canStart: false,
        message: '系統錯誤，請稍後再試'
      }
    }
  }

  // 錄音工作流程：結束錄音並提交使用量
  async endRecordingWorkflow(): Promise<{
    success: boolean
    message: string
    secondsUsed?: number
    remainingSeconds?: number
  }> {
    try {
      if (!this.hasActiveUsageSession()) {
        return {
          success: false,
          message: '沒有活動的錄音會話'
        }
      }

      // 結束會話並提交使用量
      const success = await this.usageTrackingService.endSessionAndSubmitUsage()

      if (success) {
        this.emit('usage-session-ended')

        // 獲取最新的訂閱狀態
        const subscriptionStatus = await this.authService.checkSubscriptionStatus()

        return {
          success: true,
          message: '錄音已結束，使用量已記錄',
          remainingSeconds: subscriptionStatus?.availableSecondsToday
        }
      } else {
        return {
          success: false,
          message: '結束錄音會話失敗'
        }
      }

    } catch (error) {
      console.error('❌ 結束錄音工作流程錯誤:', error)
      return {
        success: false,
        message: '系統錯誤，請稍後再試'
      }
    }
  }

  // 事件監聽
  on<K extends keyof ServiceManagerEvents>(
    event: K,
    listener: ServiceManagerEvents[K]
  ): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event)!.push(listener)
  }

  // 移除事件監聽
  off<K extends keyof ServiceManagerEvents>(
    event: K,
    listener: ServiceManagerEvents[K]
  ): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      const index = listeners.indexOf(listener)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  // 觸發事件
  private emit<K extends keyof ServiceManagerEvents>(
    event: K,
    ...args: Parameters<ServiceManagerEvents[K]>
  ): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          (listener as any)(...args)
        } catch (error) {
          console.error(`事件監聽器錯誤 (${event}):`, error)
        }
      })
    }
  }

  // 清理資源
  private async cleanup(): Promise<void> {
    try {
      console.log('正在清理服務管理器...')
      
      // 結束使用會話
      await this.usageTrackingService.endSession()
      
      // 清理服務
      this.authService.destroy()
      this.usageTrackingService.destroy()
      
      // 清理事件監聽器
      this.eventListeners.clear()
      
      console.log('服務管理器清理完成')
      
    } catch (error) {
      console.error('清理服務管理器失敗:', error)
    }
  }

  // 手動清理（用於測試或特殊情況）
  async destroy(): Promise<void> {
    await this.cleanup()
  }
}

// 單例模式
let serviceManagerInstance: ServiceManager | null = null

export function getServiceManager(): ServiceManager {
  if (!serviceManagerInstance) {
    serviceManagerInstance = new ServiceManager()
  }
  return serviceManagerInstance
}

export function destroyServiceManager(): void {
  if (serviceManagerInstance) {
    serviceManagerInstance.destroy()
    serviceManagerInstance = null
  }
}
