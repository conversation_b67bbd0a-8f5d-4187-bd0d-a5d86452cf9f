<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SpeechPilot Recording</title>
    <link rel="stylesheet" href="recording.css">
</head>
<body>
    <div class="container">
        <!-- 拖動區域 -->
        <div class="drag-area" id="dragArea"></div>

        <div class="recording-icon recording" id="recordingIcon">
            🎤
        </div>

        <div class="mode-text" id="modeText">AI 智能模式</div>
        <div class="status-text" id="statusText">正在錄音...</div>
        <div class="duration" id="duration">00:00</div>

        <div class="volume-container">
            <div class="volume-label">音量檢測</div>
            <div class="volume-bar">
                <div class="volume-fill" id="volumeFill"></div>
            </div>
            <div class="volume-text" id="volumeText">0%</div>
        </div>

        <div class="message" id="message"></div>

        <!-- 居中的媒體停止按鈕 -->
        <button class="media-stop-btn" id="stopBtn" onclick="stopRecording()">
            <span class="stop-icon">⏹</span>
            <span class="stop-text">停止錄音</span>
        </button>
    </div>

    <script src="recording.js"></script>
</body>
</html>
