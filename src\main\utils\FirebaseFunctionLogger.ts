import { httpsCallable, HttpsCallableResult } from 'firebase/functions'
import { functions } from '../../../firebase'

/**
 * Firebase Functions 調用日誌包裝器
 * 提供詳細的調用和回應日誌
 */
export class FirebaseFunctionLogger {
  private static baseUrl = 'https://asia-east1-speechpilot-f1495.cloudfunctions.net'

  /**
   * 平台名稱標準化
   */
  static normalizePlatform(platform: string): string {
    const platformMapping: Record<string, string> = {
      'win32': 'windows',
      'darwin': 'macos',
      'linux': 'linux'
    }

    return platformMapping[platform.toLowerCase()] || platform.toLowerCase()
  }

  /**
   * 調用 Firebase Function 並記錄詳細日誌
   */
  static async call<T = any, R = any>(
    functionName: string,
    data?: T,
    options?: { timeout?: number }
  ): Promise<HttpsCallableResult<R>> {
    const startTime = Date.now()
    const functionUrl = `${this.baseUrl}/${functionName}`
    
    console.log(`🔗 [Firebase Function] 調用: ${functionName}`)
    console.log(`📍 [Firebase Function] URL: ${functionUrl}`)
    
    if (data) {
      console.log(`📤 [Firebase Function] 請求參數:`, JSON.stringify(data, null, 2))
    } else {
      console.log(`📤 [Firebase Function] 請求參數: (無參數)`)
    }

    try {
      const callable = httpsCallable(functions, functionName, options)
      const result = await callable(data)
      
      const duration = Date.now() - startTime
      
      console.log(`✅ [Firebase Function] ${functionName} 調用成功 (${duration}ms)`)
      console.log(`📥 [Firebase Function] 回應:`, JSON.stringify(result.data, null, 2))
      
      return result as HttpsCallableResult<R>
      
    } catch (error: any) {
      const duration = Date.now() - startTime
      
      console.error(`❌ [Firebase Function] ${functionName} 調用失敗 (${duration}ms)`)
      console.error(`📍 [Firebase Function] URL: ${functionUrl}`)
      console.error(`🔴 [Firebase Function] 錯誤代碼: ${error.code}`)
      console.error(`🔴 [Firebase Function] 錯誤訊息: ${error.message}`)
      
      if (error.details) {
        console.error(`🔴 [Firebase Function] 錯誤詳情:`, JSON.stringify(error.details, null, 2))
      }
      
      if (error.customData) {
        console.error(`🔴 [Firebase Function] 自定義數據:`, JSON.stringify(error.customData, null, 2))
      }

      // 記錄完整的錯誤對象（用於調試）
      console.error(`🔴 [Firebase Function] 完整錯誤對象:`, {
        name: error.name,
        code: error.code,
        message: error.message,
        details: error.details,
        customData: error.customData,
        stack: error.stack
      })
      
      throw error
    }
  }

  /**
   * 批量調用多個 Functions 並記錄
   */
  static async callBatch(calls: Array<{
    name: string
    functionName: string
    data?: any
    options?: { timeout?: number }
  }>): Promise<Array<{ name: string; success: boolean; result?: any; error?: any }>> {
    console.log(`🔗 [Firebase Function] 批量調用開始，共 ${calls.length} 個函數`)
    
    const results = await Promise.allSettled(
      calls.map(async (call) => {
        try {
          const result = await this.call(call.functionName, call.data, call.options)
          return { name: call.name, success: true, result: result.data }
        } catch (error) {
          return { name: call.name, success: false, error }
        }
      })
    )

    const summary = results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value
      } else {
        return { 
          name: calls[index].name, 
          success: false, 
          error: result.reason 
        }
      }
    })

    const successCount = summary.filter(r => r.success).length
    const failureCount = summary.length - successCount

    console.log(`📊 [Firebase Function] 批量調用完成: ${successCount} 成功, ${failureCount} 失敗`)
    
    return summary
  }

  /**
   * 檢查 Firebase Functions 連接狀態
   */
  static async healthCheck(): Promise<boolean> {
    try {
      console.log(`🏥 [Firebase Function] 健康檢查開始...`)
      
      const result = await this.call('health_check')
      
      if (result.data?.status === 'healthy') {
        console.log(`✅ [Firebase Function] 健康檢查通過`)
        return true
      } else {
        console.warn(`⚠️ [Firebase Function] 健康檢查異常:`, result.data)
        return false
      }
      
    } catch (error) {
      console.error(`❌ [Firebase Function] 健康檢查失敗`)
      return false
    }
  }

  /**
   * 記錄 Function 調用統計
   */
  private static callStats = new Map<string, { count: number; totalTime: number; errors: number }>()

  static getCallStats(): Record<string, { count: number; avgTime: number; errorRate: number }> {
    const stats: Record<string, { count: number; avgTime: number; errorRate: number }> = {}
    
    this.callStats.forEach((value, key) => {
      stats[key] = {
        count: value.count,
        avgTime: value.totalTime / value.count,
        errorRate: value.errors / value.count
      }
    })
    
    return stats
  }

  static logCallStats(): void {
    const stats = this.getCallStats()
    console.log(`📊 [Firebase Function] 調用統計:`)
    
    Object.entries(stats).forEach(([functionName, stat]) => {
      console.log(`  ${functionName}: ${stat.count} 次調用, 平均 ${stat.avgTime.toFixed(0)}ms, 錯誤率 ${(stat.errorRate * 100).toFixed(1)}%`)
    })
  }
}
