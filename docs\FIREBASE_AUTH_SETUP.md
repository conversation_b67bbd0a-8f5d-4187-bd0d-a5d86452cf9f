# 🔐 Firebase Auth 設置指南

**標準認證流程實作 - 支援桌面和移動應用**

## 🎯 總覽

使用 Firebase Auth 客戶端 SDK 實作統一的認證流程：
- ✅ **桌面應用** - 彈出視窗登入
- ✅ **iOS 應用** - 原生 Google Sign-In
- ✅ **Android 應用** - 原生 Google Sign-In
- ✅ **無需自建 OAuth 端點** - Firebase 處理所有複雜性

## 🔧 第一步：Firebase Console 配置

### 1. 啟用 Google 登入
1. 前往 [Firebase Console](https://console.firebase.google.com/project/speechpilot-f1495)
2. 點擊 **Authentication** → **Sign-in method**
3. 啟用 **Google** 登入提供者
4. 設置 **Project support email**

### 2. 配置 OAuth 同意畫面
1. 前往 [Google Cloud Console](https://console.cloud.google.com/apis/credentials/consent)
2. 選擇專案：`speechpilot-f1495`
3. 配置 OAuth 同意畫面：
   - **應用程式名稱**: SpeechPilot
   - **用戶支援電子郵件**: 你的電子郵件
   - **授權網域**: 添加你的網域（如果有）

### 3. 獲取配置資訊
從 Firebase Console → Project Settings → General 獲取：
```javascript
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "speechpilot-f1495.firebaseapp.com",
  projectId: "speechpilot-f1495",
  storageBucket: "speechpilot-f1495.appspot.com",
  messagingSenderId: "your-sender-id",
  appId: "your-app-id"
};
```

## 💻 第二步：桌面應用實作

### 安裝依賴
```bash
npm install firebase
```

### 基本設置
```javascript
// firebase-config.js
import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFunctions } from 'firebase/functions';

const firebaseConfig = {
  // 你的 Firebase 配置
};

const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);
export const functions = getFunctions(app, 'asia-east1'); // 重要：指定區域
```

### Google 登入實作
```javascript
// auth-service.js
import { signInWithPopup, GoogleAuthProvider, signOut } from 'firebase/auth';
import { auth, functions } from './firebase-config.js';
import { httpsCallable } from 'firebase/functions';

const provider = new GoogleAuthProvider();

// 配置 Google 登入範圍
provider.addScope('email');
provider.addScope('profile');

export const authService = {
  // Google 登入
  async signInWithGoogle() {
    try {
      const result = await signInWithPopup(auth, provider);
      const user = result.user;
      const token = await user.getIdToken();
      
      console.log('登入成功:', user.email);
      
      // 驗證 token 並獲取用戶資訊
      const verifyToken = httpsCallable(functions, 'verify_token');
      const tokenResult = await verifyToken();
      
      return {
        success: true,
        user: {
          uid: user.uid,
          email: user.email,
          displayName: user.displayName,
          photoURL: user.photoURL
        },
        token,
        userData: tokenResult.data
      };
    } catch (error) {
      console.error('登入失敗:', error);
      return {
        success: false,
        error: error.message
      };
    }
  },

  // 登出
  async signOut() {
    try {
      await signOut(auth);
      return { success: true };
    } catch (error) {
      console.error('登出失敗:', error);
      return { success: false, error: error.message };
    }
  },

  // 獲取當前用戶
  getCurrentUser() {
    return auth.currentUser;
  },

  // 監聽認證狀態
  onAuthStateChanged(callback) {
    return auth.onAuthStateChanged(callback);
  }
};
```

### 完整的應用整合
```javascript
// app.js
import { authService } from './auth-service.js';
import { httpsCallable } from 'firebase/functions';
import { functions } from './firebase-config.js';

class SpeechPilotApp {
  constructor() {
    this.user = null;
    this.setupAuthListener();
  }

  setupAuthListener() {
    authService.onAuthStateChanged(async (user) => {
      if (user) {
        console.log('用戶已登入:', user.email);
        this.user = user;
        await this.initializeUserSession();
      } else {
        console.log('用戶未登入');
        this.user = null;
        this.showLoginScreen();
      }
    });
  }

  async handleGoogleSignIn() {
    const result = await authService.signInWithGoogle();
    
    if (result.success) {
      console.log('登入成功，用戶資料:', result.userData);
      // 登入成功後的處理
    } else {
      console.error('登入失敗:', result.error);
      // 顯示錯誤訊息
    }
  }

  async initializeUserSession() {
    try {
      // 1. 註冊設備
      const registerDevice = httpsCallable(functions, 'register_device_v2');
      const deviceResult = await registerDevice({
        device_id: this.getDeviceId(),
        device_name: this.getDeviceName(),
        platform: this.getPlatform(),
        app_version: '1.0.0',
        device_info: this.getDeviceInfo()
      });

      if (deviceResult.data.success) {
        console.log('設備註冊成功');
        
        // 2. 驗證設備存取
        const validateDevice = httpsCallable(functions, 'validate_device');
        const accessResult = await validateDevice({
          device_id: this.getDeviceId(),
          platform: this.getPlatform()
        });

        if (accessResult.data.has_access) {
          console.log('設備驗證成功，開始使用應用');
          this.startApp();
        } else {
          console.log('設備存取被拒絕:', accessResult.data.reason);
          this.handleAccessDenied(accessResult.data);
        }
      }
    } catch (error) {
      console.error('初始化用戶會話失敗:', error);
    }
  }

  // 工具函數
  getDeviceId() {
    // 生成或獲取設備唯一ID
    let deviceId = localStorage.getItem('device_id');
    if (!deviceId) {
      deviceId = 'desktop-' + Math.random().toString(36).substr(2, 9);
      localStorage.setItem('device_id', deviceId);
    }
    return deviceId;
  }

  getDeviceName() {
    return navigator.platform || 'Desktop Computer';
  }

  getPlatform() {
    if (navigator.platform.includes('Win')) return 'windows';
    if (navigator.platform.includes('Mac')) return 'macos';
    return 'desktop';
  }

  getDeviceInfo() {
    return {
      user_agent: navigator.userAgent,
      screen_resolution: `${screen.width}x${screen.height}`,
      language: navigator.language
    };
  }

  showLoginScreen() {
    // 顯示登入界面
    document.getElementById('login-screen').style.display = 'block';
    document.getElementById('app-content').style.display = 'none';
  }

  startApp() {
    // 隱藏登入界面，顯示應用內容
    document.getElementById('login-screen').style.display = 'none';
    document.getElementById('app-content').style.display = 'block';
  }

  handleAccessDenied(data) {
    // 處理存取被拒絕的情況
    alert(`存取被拒絕: ${data.reason}`);
  }
}

// 初始化應用
const app = new SpeechPilotApp();

// 綁定登入按鈕
document.getElementById('google-signin-btn').addEventListener('click', () => {
  app.handleGoogleSignIn();
});

// 綁定登出按鈕
document.getElementById('signout-btn').addEventListener('click', async () => {
  await authService.signOut();
});
```

### HTML 範例
```html
<!DOCTYPE html>
<html>
<head>
    <title>SpeechPilot</title>
</head>
<body>
    <!-- 登入畫面 -->
    <div id="login-screen">
        <h1>SpeechPilot</h1>
        <button id="google-signin-btn">使用 Google 登入</button>
    </div>

    <!-- 應用內容 -->
    <div id="app-content" style="display: none;">
        <h1>歡迎使用 SpeechPilot</h1>
        <button id="signout-btn">登出</button>
        <!-- 你的應用內容 -->
    </div>

    <script type="module" src="app.js"></script>
</body>
</html>
```

## 📱 第三步：移動應用準備

### iOS 配置
1. 在 Firebase Console 中添加 iOS 應用
2. 下載 `GoogleService-Info.plist`
3. 在 Xcode 中添加 URL Scheme

### Android 配置
1. 在 Firebase Console 中添加 Android 應用
2. 下載 `google-services.json`
3. 配置 SHA-1 指紋

## 🚀 立即行動清單

### 你需要做的（5 分鐘）：
- [ ] 在 Firebase Console 啟用 Google 登入
- [ ] 配置 OAuth 同意畫面
- [ ] 提供 Firebase 配置給前端團隊

### 前端團隊需要做的（1-2 天）：
- [ ] 安裝 Firebase SDK
- [ ] 實作上述認證代碼
- [ ] 測試登入流程
- [ ] 整合設備註冊和驗證

## 🎯 優勢總結

✅ **無需自建 OAuth 端點** - 省去服務器端複雜性  
✅ **跨平台一致性** - 桌面和移動使用相同流程  
✅ **安全性最佳** - Google 官方維護  
✅ **維護成本低** - Firebase 處理所有更新  
✅ **快速實作** - 幾天內完成整合  

這個方案完全解決了你的 "Page not found" 問題，因為不再需要 `/auth/google` 端點！
