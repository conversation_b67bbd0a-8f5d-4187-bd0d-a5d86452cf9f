/* SpeechPilot 登入視窗樣式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    overflow-x: hidden;
}

.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

/* 標題區域 */
.header {
    text-align: center;
    margin-bottom: 30px;
}

.logo {
    font-size: 48px;
    margin-bottom: 10px;
}

.header h1 {
    font-size: 32px;
    font-weight: 700;
    color: white;
    margin-bottom: 5px;
}

.subtitle {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 300;
}

/* 登入表單 */
.login-form {
    background: white;
    border-radius: 16px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
    position: relative;
    margin-bottom: 30px;
}

.login-form h2 {
    font-size: 24px;
    font-weight: 600;
    text-align: center;
    margin-bottom: 8px;
    color: #333;
}

.description {
    text-align: center;
    color: #666;
    margin-bottom: 30px;
    font-size: 14px;
}

/* 載入覆蓋層 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

#loadingText {
    color: #666;
    font-size: 14px;
}

/* 錯誤訊息 */
.error-message {
    background: #fee;
    border: 1px solid #fcc;
    color: #c33;
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 14px;
    text-align: center;
}

/* SSO 按鈕 */
.sso-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 30px;
}

.sso-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 14px 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: white;
    color: #333;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.sso-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.sso-btn:active {
    transform: translateY(0);
}

.sso-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.sso-icon {
    width: 20px;
    height: 20px;
}

.google-btn {
    border-color: #4285F4;
}

.google-btn:hover {
    background: #f8f9ff;
    border-color: #4285F4;
}

.facebook-btn {
    border-color: #1877F2;
}

.facebook-btn:hover {
    background: #f8f9ff;
    border-color: #1877F2;
}

.apple-btn {
    border-color: #000;
}

.apple-btn:hover {
    background: #f8f8f8;
    border-color: #000;
}

/* 條款和隱私 */
.terms {
    text-align: center;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
    margin-bottom: 20px;
}

.terms a {
    color: #667eea;
    text-decoration: none;
}

.terms a:hover {
    text-decoration: underline;
}

/* 關閉按鈕 */
.close-section {
    text-align: center;
}

.close-btn {
    background: none;
    border: none;
    color: #999;
    font-size: 14px;
    cursor: pointer;
    padding: 8px 16px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: #f5f5f5;
    color: #666;
}

/* 功能介紹 */
.features {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    width: 100%;
    max-width: 400px;
}

.features h3 {
    color: white;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    margin-bottom: 15px;
}

.feature-list {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
}

.feature-icon {
    font-size: 16px;
}

/* 響應式設計 */
@media (max-width: 480px) {
    .container {
        padding: 15px;
    }
    
    .login-form {
        padding: 30px 20px;
    }
    
    .header h1 {
        font-size: 28px;
    }
    
    .feature-list {
        grid-template-columns: 1fr;
    }
}
