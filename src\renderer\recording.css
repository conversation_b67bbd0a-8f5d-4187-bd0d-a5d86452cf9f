* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #2c3e50;
    color: #ecf0f1;
    border-radius: 16px;
    overflow: hidden;
    border: 1px solid #34495e;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    zoom: 1;  /* 確保縮放比例正確 */
    transform: scale(1);  /* 防止意外縮放 */
}

.container {
    padding: 25px;
    text-align: center;
    height: 350px;  /* 增加高度到 350px */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
}

/* 拖動區域 */
.drag-area {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 40px;
    -webkit-app-region: drag;
    z-index: 10;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px 16px 0 0;
}

/* 整個容器也可以拖動，但排除按鈕 */
.container {
    -webkit-app-region: drag;
}

.recording-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    transition: all 0.3s ease;
}

.recording-icon.recording {
    background: linear-gradient(45deg, #ff4757, #ff6b7a);
    animation: pulse 1.5s infinite;
}

.recording-icon.processing {
    background: linear-gradient(45deg, #3742fa, #5352ed);
    animation: spin 1s linear infinite;
}

.recording-icon.completed {
    background: linear-gradient(45deg, #2ed573, #7bed9f);
}

.recording-icon.error {
    background: linear-gradient(45deg, #ff4757, #ff3838);
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.mode-text {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #ecf0f1;
}

.status-text {
    font-size: 14px;
    color: #bdc3c7;
    margin-bottom: 8px;
}

.duration {
    font-size: 12px;
    color: #95a5a6;
    font-family: 'Courier New', monospace;
}

.message {
    font-size: 12px;
    color: #f39c12;
    margin-top: 8px;
    max-width: 350px;
    word-wrap: break-word;
}

.volume-container {
    width: 100%;
    max-width: 300px;
    margin: 10px 0;
}

.volume-label {
    font-size: 11px;
    color: #95a5a6;
    margin-bottom: 5px;
}

.volume-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.volume-fill {
    height: 100%;
    background: linear-gradient(90deg, #2ed573, #fffa65, #ff4757);
    border-radius: 4px;
    transition: width 0.1s ease;
    width: 0%;
}

.volume-text {
    font-size: 10px;
    color: #95a5a6;
    margin-top: 3px;
    text-align: center;
}

/* 媒體停止按鈕 */
.media-stop-btn {
    margin-top: 20px;
    padding: 12px 24px;
    border: none;
    background: linear-gradient(45deg, #ff4757, #ff3838);
    color: white;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 71, 87, 0.3);
    min-width: 120px;
    -webkit-app-region: no-drag;
    position: relative;
    z-index: 20;
}

.media-stop-btn:hover:not(.stopping):not(.stopped) {
    background: linear-gradient(45deg, #ff3838, #ff2f2f);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 71, 87, 0.4);
}

.media-stop-btn:active:not(.stopping):not(.stopped) {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(255, 71, 87, 0.3);
}

/* 停止中狀態 */
.media-stop-btn.stopping {
    background: linear-gradient(45deg, #ffa502, #ff6348);
    cursor: not-allowed;
    animation: pulse-stopping 1.5s infinite;
}

/* 已停止狀態 */
.media-stop-btn.stopped {
    background: linear-gradient(45deg, #2ed573, #7bed9f);
    cursor: default;
}

.stop-icon {
    font-size: 16px;
}

.stop-text {
    font-size: 13px;
}

/* 停止按鈕動畫 */
@keyframes pulse-stopping {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}
