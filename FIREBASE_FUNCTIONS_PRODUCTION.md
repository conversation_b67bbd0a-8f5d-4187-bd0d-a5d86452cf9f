# 🚀 SpeechPilot 生產級 Firebase Functions 規範

## 概述

這份文檔定義了 SpeechPilot 桌面應用程式所需的 Firebase Functions，實現企業級 SSO 認證和使用量管理。

## 🔐 認證相關 Functions

### 1. OAuth 認證端點

#### `/auth/google`
- **方法**: GET
- **用途**: 啟動 Google OAuth 流程
- **參數**:
  - `state`: CSRF 防護參數
  - `platform`: 平台標識 (desktop/web)
- **流程**:
  1. 重定向到 Google OAuth
  2. 用戶授權後回調到 Firebase Functions
  3. 交換授權碼獲取用戶資訊
  4. 生成 Firebase Custom Token
  5. 重定向到 `speechpilot://oauth/callback?token=xxx&state=xxx`

#### `/auth/facebook`
- **方法**: GET
- **用途**: 啟動 Facebook OAuth 流程
- **參數**: 同 Google
- **流程**: 同 Google，但使用 Facebook API

#### `/auth/apple`
- **方法**: GET
- **用途**: 啟動 Apple OAuth 流程
- **參數**: 同 Google
- **流程**: 同 Google，但使用 Apple Sign In

### 2. 用戶管理

#### `createOrUpdateUser`
- **方法**: HTTPS Callable
- **用途**: 創建或更新用戶記錄
- **輸入**:
```typescript
{
  uid: string
  email: string
  name?: string
  image?: string
  authProvider: 'google' | 'facebook' | 'apple'
  emailVerified: boolean
  platform: string
  appVersion: string
}
```
- **輸出**:
```typescript
{
  success: boolean
  user: {
    id: string
    email: string
    subscriptionPlan: string
    isActive: boolean
  }
}
```

## 📊 使用量管理 Functions

### 1. 訂閱狀態檢查

#### `checkUserSubscriptionStatus`
- **方法**: HTTPS Callable
- **用途**: 檢查用戶訂閱狀態和可用時間
- **輸入**: 無（使用 Firebase Auth context）
- **輸出**:
```typescript
{
  canUse: boolean
  availableSecondsToday: number
  availableSecondsThisMonth: number
  needsUpgrade: boolean
  upgradeUrl?: string
  plan: string
}
```

### 2. 錄音前檢查

#### `checkUsageBeforeRecording`
- **方法**: HTTPS Callable
- **用途**: 錄音前檢查可用時間
- **輸入**:
```typescript
{
  estimatedDurationSeconds: number
}
```
- **輸出**:
```typescript
{
  canRecord: boolean
  availableSeconds: number
  reason?: string
  upgradeRequired?: boolean
  upgradeUrl?: string
}
```

### 3. 使用量計算

#### `calculateUsageAfterRecording`
- **方法**: HTTPS Callable
- **用途**: 錄音後計算和扣除使用量
- **輸入**:
```typescript
{
  deviceId: string
  sessionStart: string
  sessionEnd: string
  features: string[]
  platform: string
  appVersion: string
}
```
- **輸出**:
```typescript
{
  success: boolean
  secondsUsed: number
  tokensUsed?: number
  remainingSeconds: number
}
```

## 🔧 設備管理 Functions

### 1. 設備註冊

#### `registerDevice`
- **方法**: HTTPS Callable
- **用途**: 註冊新設備
- **輸入**:
```typescript
{
  deviceId: string
  deviceName: string
  platform: 'windows' | 'macos' | 'ios' | 'android'
  deviceFingerprint: string
  appVersion: string
  osVersion: string
}
```
- **輸出**:
```typescript
{
  success: boolean
  deviceId: string
}
```

## 🗄️ Firestore 數據結構

### Users Collection (`/users/{uid}`)
```typescript
{
  uid: string
  email: string
  name?: string
  image?: string
  authProvider: string
  subscriptionPlan: 'free' | 'pro' | 'enterprise'
  subscriptionStatus: 'active' | 'cancelled' | 'expired'
  isActive: boolean
  isBanned: boolean
  createdAt: Timestamp
  lastLoginAt: Timestamp
  
  // 使用量限制
  dailyLimitSeconds: number
  monthlyLimitSeconds: number
  
  // 當前使用量
  usage: {
    today: {
      date: string // YYYY-MM-DD
      secondsUsed: number
      tokensUsed: number
    }
    thisMonth: {
      month: string // YYYY-MM
      secondsUsed: number
      tokensUsed: number
    }
  }
}
```

### Devices Collection (`/users/{uid}/devices/{deviceId}`)
```typescript
{
  deviceId: string
  deviceName: string
  platform: string
  deviceFingerprint: string
  appVersion: string
  osVersion: string
  isActive: boolean
  lastActiveAt: Timestamp
  createdAt: Timestamp
}
```

### Usage Sessions Collection (`/users/{uid}/sessions/{sessionId}`)
```typescript
{
  sessionId: string
  deviceId: string
  startTime: Timestamp
  endTime?: Timestamp
  features: string[]
  platform: string
  appVersion: string
  secondsUsed?: number
  tokensUsed?: number
  status: 'active' | 'completed' | 'failed'
}
```

## 🛡️ 安全考量

### 1. 認證驗證
- 所有 Callable Functions 必須驗證 Firebase Auth token
- 檢查用戶是否為活躍狀態且未被封禁

### 2. 使用量防護
- 實施服務器端使用量驗證
- 防止客戶端時間操作
- 記錄所有使用量變更的審計日誌

### 3. 設備管理
- 限制每用戶最大設備數量
- 檢測異常設備註冊模式
- 實施設備指紋驗證

## 🚀 部署配置

### 環境變數
```bash
# OAuth 配置
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
APPLE_CLIENT_ID=your-apple-client-id
APPLE_PRIVATE_KEY=your-apple-private-key

# 應用程式配置
SPEECHPILOT_DEEP_LINK_SCHEME=speechpilot
WEB_PORTAL_URL=https://your-web-portal.com

# 訂閱計劃配置
FREE_PLAN_DAILY_SECONDS=300
PRO_PLAN_DAILY_SECONDS=3600
ENTERPRISE_PLAN_DAILY_SECONDS=unlimited
```

### Firebase 配置
- 啟用 Authentication (Google, Facebook, Apple)
- 配置 Firestore 安全規則
- 設置 Functions 的 CORS 政策
- 配置自定義域名（可選）

## 📝 實施優先級

1. **高優先級**:
   - OAuth 認證端點
   - 基本用戶管理
   - 簡單使用量檢查

2. **中優先級**:
   - 詳細使用量追蹤
   - 設備管理
   - 訂閱狀態管理

3. **低優先級**:
   - 高級安全功能
   - 審計日誌
   - 分析和報告

這個架構提供了企業級的安全性和可擴展性，同時保持了良好的用戶體驗。
