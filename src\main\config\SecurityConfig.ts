// 安全配置和防護機制

export interface SecurityConfig {
  // 使用量追蹤安全設定
  usageTracking: {
    maxSessionDurationHours: number // 最大會話時間
    minSubmissionIntervalSeconds: number // 最小提交間隔
    maxClockSkewMinutes: number // 最大時鐘偏差
    enableClientValidation: boolean // 是否啟用客戶端驗證
    enableServerValidation: boolean // 是否啟用伺服器驗證
  }
  
  // 認證安全設定
  authentication: {
    tokenRefreshIntervalMinutes: number // Token 刷新間隔
    maxLoginAttempts: number // 最大登入嘗試次數
    loginAttemptWindowMinutes: number // 登入嘗試時間窗口
    requireEmailVerification: boolean // 是否需要郵件驗證
  }
  
  // 設備安全設定
  device: {
    enableDeviceFingerprinting: boolean // 是否啟用設備指紋
    maxDevicesPerUser: number // 每用戶最大設備數
    deviceInactivityDays: number // 設備非活動天數
    requireDeviceRegistration: boolean // 是否需要設備註冊
  }
  
  // API 安全設定
  api: {
    enableRateLimiting: boolean // 是否啟用速率限制
    maxRequestsPerMinute: number // 每分鐘最大請求數
    enableRequestLogging: boolean // 是否啟用請求日誌
    enableIPWhitelist: boolean // 是否啟用 IP 白名單
  }
  
  // 資料保護設定
  dataProtection: {
    enableEncryption: boolean // 是否啟用加密
    encryptionAlgorithm: string // 加密演算法
    enableDataMasking: boolean // 是否啟用資料遮罩
    dataRetentionDays: number // 資料保留天數
  }
}

// 預設安全配置
export const DEFAULT_SECURITY_CONFIG: SecurityConfig = {
  usageTracking: {
    maxSessionDurationHours: 24,
    minSubmissionIntervalSeconds: 5,
    maxClockSkewMinutes: 5,
    enableClientValidation: true,
    enableServerValidation: true
  },
  
  authentication: {
    tokenRefreshIntervalMinutes: 30,
    maxLoginAttempts: 5,
    loginAttemptWindowMinutes: 15,
    requireEmailVerification: false // SSO 提供者已驗證
  },
  
  device: {
    enableDeviceFingerprinting: true,
    maxDevicesPerUser: 10, // 會根據訂閱計劃調整
    deviceInactivityDays: 30,
    requireDeviceRegistration: true
  },
  
  api: {
    enableRateLimiting: true,
    maxRequestsPerMinute: 60,
    enableRequestLogging: true,
    enableIPWhitelist: false
  },
  
  dataProtection: {
    enableEncryption: true,
    encryptionAlgorithm: 'AES-256-GCM',
    enableDataMasking: true,
    dataRetentionDays: 365
  }
}

// 安全驗證器
export class SecurityValidator {
  private config: SecurityConfig
  
  constructor(config: SecurityConfig = DEFAULT_SECURITY_CONFIG) {
    this.config = config
  }
  
  // 驗證會話時間
  validateSessionDuration(sessionStart: Date, sessionEnd: Date): boolean {
    const durationHours = (sessionEnd.getTime() - sessionStart.getTime()) / (1000 * 60 * 60)
    return durationHours <= this.config.usageTracking.maxSessionDurationHours
  }
  
  // 驗證時鐘偏差
  validateClockSkew(clientTime: Date, serverTime: Date = new Date()): boolean {
    const skewMinutes = Math.abs(clientTime.getTime() - serverTime.getTime()) / (1000 * 60)
    return skewMinutes <= this.config.usageTracking.maxClockSkewMinutes
  }
  
  // 驗證提交間隔
  validateSubmissionInterval(lastSubmission: Date, currentSubmission: Date): boolean {
    const intervalSeconds = (currentSubmission.getTime() - lastSubmission.getTime()) / 1000
    return intervalSeconds >= this.config.usageTracking.minSubmissionIntervalSeconds
  }
  
  // 驗證設備指紋
  validateDeviceFingerprint(fingerprint: string): boolean {
    if (!this.config.device.enableDeviceFingerprinting) {
      return true
    }
    
    // 檢查指紋格式和長度
    return typeof fingerprint === 'string' && 
           fingerprint.length >= 8 && 
           fingerprint.length <= 64 &&
           /^[a-f0-9]+$/i.test(fingerprint)
  }
  
  // 驗證 API 請求頻率
  private requestCounts: Map<string, { count: number, resetTime: number }> = new Map()
  
  validateRequestRate(identifier: string): boolean {
    if (!this.config.api.enableRateLimiting) {
      return true
    }
    
    const now = Date.now()
    const windowMs = 60 * 1000 // 1 分鐘
    
    const record = this.requestCounts.get(identifier)
    
    if (!record || now > record.resetTime) {
      // 新的時間窗口
      this.requestCounts.set(identifier, {
        count: 1,
        resetTime: now + windowMs
      })
      return true
    }
    
    if (record.count >= this.config.api.maxRequestsPerMinute) {
      return false
    }
    
    record.count++
    return true
  }
  
  // 清理過期的請求記錄
  cleanupRequestCounts(): void {
    const now = Date.now()
    for (const [key, record] of this.requestCounts.entries()) {
      if (now > record.resetTime) {
        this.requestCounts.delete(key)
      }
    }
  }
  
  // 驗證資料完整性
  validateDataIntegrity(data: any, expectedFields: string[]): boolean {
    if (!data || typeof data !== 'object') {
      return false
    }
    
    return expectedFields.every(field => data.hasOwnProperty(field))
  }
  
  // 生成安全的隨機 ID
  generateSecureId(): string {
    const crypto = require('crypto')
    return crypto.randomUUID()
  }
  
  // 生成安全的隨機字串
  generateSecureToken(length: number = 32): string {
    const crypto = require('crypto')
    return crypto.randomBytes(length).toString('hex')
  }
  
  // 雜湊敏感資料
  hashSensitiveData(data: string, salt?: string): string {
    const crypto = require('crypto')
    const actualSalt = salt || crypto.randomBytes(16).toString('hex')
    const hash = crypto.pbkdf2Sync(data, actualSalt, 10000, 64, 'sha512')
    return `${actualSalt}:${hash.toString('hex')}`
  }
  
  // 驗證雜湊
  verifyHash(data: string, hash: string): boolean {
    try {
      const [salt, originalHash] = hash.split(':')
      const crypto = require('crypto')
      const verifyHash = crypto.pbkdf2Sync(data, salt, 10000, 64, 'sha512')
      return originalHash === verifyHash.toString('hex')
    } catch (error) {
      return false
    }
  }
  
  // 遮罩敏感資料
  maskSensitiveData(data: string, visibleChars: number = 4): string {
    if (!this.config.dataProtection.enableDataMasking) {
      return data
    }
    
    if (data.length <= visibleChars * 2) {
      return '*'.repeat(data.length)
    }
    
    const start = data.substring(0, visibleChars)
    const end = data.substring(data.length - visibleChars)
    const middle = '*'.repeat(data.length - visibleChars * 2)
    
    return `${start}${middle}${end}`
  }
  
  // 記錄安全事件
  logSecurityEvent(event: string, details: any, severity: 'low' | 'medium' | 'high' = 'medium'): void {
    if (!this.config.api.enableRequestLogging) {
      return
    }
    
    const logEntry = {
      timestamp: new Date().toISOString(),
      event,
      severity,
      details: this.config.dataProtection.enableDataMasking ? 
        this.sanitizeLogData(details) : details
    }
    
    console.log(`[SECURITY-${severity.toUpperCase()}]`, JSON.stringify(logEntry))
  }
  
  // 清理日誌資料
  private sanitizeLogData(data: any): any {
    if (typeof data !== 'object' || data === null) {
      return data
    }
    
    const sensitiveFields = ['password', 'token', 'key', 'secret', 'email', 'phone']
    const sanitized = { ...data }
    
    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = this.maskSensitiveData(String(sanitized[field]))
      }
    }
    
    return sanitized
  }
  
  // 更新配置
  updateConfig(newConfig: Partial<SecurityConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }
  
  // 獲取當前配置
  getConfig(): SecurityConfig {
    return { ...this.config }
  }
}

// 單例安全驗證器
let securityValidatorInstance: SecurityValidator | null = null

export function getSecurityValidator(): SecurityValidator {
  if (!securityValidatorInstance) {
    securityValidatorInstance = new SecurityValidator()
  }
  return securityValidatorInstance
}

export function createSecurityValidator(config?: SecurityConfig): SecurityValidator {
  return new SecurityValidator(config)
}
