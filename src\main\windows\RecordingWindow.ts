import { BrowserWindow, screen } from 'electron'
import { join } from 'path'

export interface RecordingStatus {
  isRecording: boolean
  mode: 'ai' | 'direct'
  duration: number
  status: 'recording' | 'processing' | 'completed' | 'error'
  message?: string
  volume?: number  // 0-100 音量級別
}

export class RecordingWindow {
  private window: BrowserWindow | null = null
  private status: RecordingStatus = {
    isRecording: false,
    mode: 'ai',
    duration: 0,
    status: 'recording'
  }

  create(): void {
    console.log('🪟 Creating recording window...')

    if (this.window) {
      console.log('🪟 Window already exists, focusing...')
      this.window.focus()
      this.window.show()  // 確保視窗顯示
      return
    }

    // 獲取主顯示器的工作區域
    const primaryDisplay = screen.getPrimaryDisplay()
    const { width, height } = primaryDisplay.workAreaSize
    console.log('🖥️ Display size:', { width, height })

    this.window = new BrowserWindow({
      width: 400,
      height: 350,  // 增加高度 100 像素 (從 250 到 350)
      x: Math.round((width - 400) / 2),
      y: Math.round((height - 350) / 2),
      resizable: false,
      minimizable: false,
      maximizable: false,
      alwaysOnTop: true,
      skipTaskbar: true,
      frame: false,
      transparent: false,  // 關閉透明度
      backgroundColor: '#2c3e50',  // 設置背景色
      show: false,  // 不自動顯示，避免搶奪焦點
      focusable: true,  // 改為可獲得焦點，方便調試
      movable: true,  // 允許拖動
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        zoomFactor: 1.0  // 確保縮放比例為 100%
      }
    })

    console.log('🪟 BrowserWindow created with options')

    // 載入獨立的 HTML 文件 - 指向源文件
    const htmlPath = join(process.cwd(), 'src/renderer/recording.html')
    console.log('🪟 Loading HTML file:', htmlPath)

    // 添加更多事件監聽器來調試
    this.window.webContents.on('did-start-loading', () => {
      console.log('🔄 Window started loading')
    })

    this.window.webContents.on('did-finish-load', () => {
      console.log('✅ Window finished loading')
    })

    this.window.webContents.on('did-fail-load', (_event, errorCode, errorDescription) => {
      console.error('❌ Window failed to load:', errorCode, errorDescription)
    })

    this.window.on('ready-to-show', () => {
      console.log('🪟 Window ready to show')
      this.window?.show()  // 直接顯示視窗
    })

    this.window.loadFile(htmlPath)
      .then(() => {
        console.log('🪟 HTML loaded successfully')
        // 確保視窗顯示
        if (this.window && !this.window.isDestroyed()) {
          this.window.show()
          console.log('🪟 Window shown after load')
        }
      })
      .catch((error) => {
        console.error('❌ Failed to load HTML:', error)
      })

    // 窗口關閉事件
    this.window.on('closed', () => {
      // 通知主進程停止錄音
      const { ipcMain } = require('electron')
      ipcMain.emit('recording-window-closed')
      this.window = null
    })

    // 發送初始狀態
    this.window.webContents.once('dom-ready', () => {
      console.log('🪟 DOM ready, updating status')
      this.updateStatus(this.status)
    })

    console.log('🪟 Recording window created')
  }

  updateStatus(status: Partial<RecordingStatus>): void {
    this.status = { ...this.status, ...status }
    
    if (this.window && !this.window.isDestroyed()) {
      this.window.webContents.send('status-update', this.status)
    }
  }

  close(delay: number = 1000): void {
    if (this.window && !this.window.isDestroyed()) {
      setTimeout(() => {
        if (this.window && !this.window.isDestroyed()) {
          this.window.close()
        }
      }, delay)
    }
  }

  isVisible(): boolean {
    return this.window !== null && !this.window.isDestroyed()
  }

  focus(): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.focus()
    }
  }

  getWindow(): BrowserWindow | null {
    return this.window
  }
}
