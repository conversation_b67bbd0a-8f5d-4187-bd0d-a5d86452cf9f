# SpeakOneAI 環境變數設定範例
# 複製此檔案為 .env 並填入你的實際值

# Azure OpenAI 設定
AZURE_OPENAI_ENDPOINT=https://johnn-m6ix9yz4-eastus2.openai.azure.com/
AZURE_OPENAI_API_KEY=B1IEVn3J0ptwnnA0Fzyp6i3DRSou25meMJX6IEzlZg18d21reTabJQQJ99BAACHYHv6XJ3w3AAAAACOGABAv
AZURE_OPENAI_MODEL=gpt-4o-mini-audio-preview
AZURE_OPENAI_TRANSCRIBE_MODEL=gpt-4o-transcribe

# Firebase 設定（用於 SSO 認證和使用量追蹤）
FIREBASE_API_KEY=AIzaSyClbeEq7o4kXcDLcO40r7KhBAmIfIMVARA
FIREBASE_AUTH_DOMAIN=speechpilot-f1495.firebaseapp.com
FIREBASE_PROJECT_ID=speechpilot-f1495
FIREBASE_STORAGE_BUCKET=speechpilot-f1495.firebasestorage.app
FIREBASE_MESSAGING_SENDER_ID=1005638354561
FIREBASE_APP_ID=1:1005638354561:web:04959935ed09084298af80

# Firebase Functions URL（用於後端 API）
# 生產環境: https://your-region-your-project.cloudfunctions.net
# 測試環境: http://localhost:3000
FIREBASE_FUNCTIONS_URL=https://asia-east1-speechpilot-f1495.cloudfunctions.net

# OAuth 配置（需要在 Firebase Console 和各 OAuth 提供商設置）
GOOGLE_CLIENT_ID=1005638354561-nkgds4pma1gi78mhab71e1m6je69ulbj.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
FACEBOOK_APP_ID=your-facebook-app-id
APPLE_CLIENT_ID=your-apple-client-id

# Web Portal URL（用於升級連結和用戶管理）
# 這是用戶可以管理訂閱、查看使用量、升級計劃的網站
WEB_PORTAL_URL=https://speakoneai.com

# Deep Link 配置
DEEP_LINK_SCHEME=speakoneai

# 開發環境設定
NODE_ENV=production
USE_FIREBASE_EMULATOR=false
ENABLE_MOCK_AUTH=false