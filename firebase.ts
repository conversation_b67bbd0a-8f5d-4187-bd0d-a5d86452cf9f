// Firebase 配置和初始化
import { initializeApp } from 'firebase/app'
import { getAuth, connectAuthEmulator, User } from 'firebase/auth'
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore'
import { getFunctions, connectFunctionsEmulator } from 'firebase/functions'
import { config } from 'dotenv'
import { join } from 'path'

// 確保環境變數已載入
const envPath = join(process.cwd(), '.env.local')
config({ path: envPath })

// Firebase 配置 - 從環境變數讀取
const firebaseConfig = {
  apiKey: process.env.FIREBASE_API_KEY || "your-api-key",
  authDomain: process.env.FIREBASE_AUTH_DOMAIN || "your-project.firebaseapp.com",
  projectId: process.env.FIREBASE_PROJECT_ID || "your-project-id",
  storageBucket: process.env.FIREBASE_STORAGE_BUCKET || "your-project.appspot.com",
  messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID || "123456789",
  appId: process.env.FIREBASE_APP_ID || "your-app-id"
}

// 調試：檢查 Firebase 配置
console.log('🔧 Firebase Config Debug:', {
  apiKey: firebaseConfig.apiKey ? `${firebaseConfig.apiKey.substring(0, 10)}...` : 'MISSING',
  authDomain: firebaseConfig.authDomain,
  projectId: firebaseConfig.projectId,
  hasValidApiKey: firebaseConfig.apiKey !== "your-api-key" && firebaseConfig.apiKey.length > 10
})

// 初始化 Firebase
const app = initializeApp(firebaseConfig)

// 初始化服務
export const auth = getAuth(app)
export const db = getFirestore(app)
export const functions = getFunctions(app, 'asia-east1') // 重要：指定區域

// 開發環境連接模擬器
if (process.env.NODE_ENV === 'development' && process.env.USE_FIREBASE_EMULATOR === 'true') {
  try {
    connectAuthEmulator(auth, 'http://localhost:9099')
    connectFirestoreEmulator(db, 'localhost', 8080)
    connectFunctionsEmulator(functions, 'localhost', 5001)
    console.log('🔧 Connected to Firebase Emulators')
  } catch (error) {
    console.log('⚠️ Firebase Emulators already connected or not available')
  }
}

// 類型定義
export type FirebaseUser = User

// 認證相關函數
import {
  signInWithCredential,
  GoogleAuthProvider,
  FacebookAuthProvider,
  OAuthProvider,
  signOut as firebaseSignOut
} from 'firebase/auth'
// import { httpsCallable } from 'firebase/functions' // 已移至 FirebaseFunctionLogger

// 設置 OAuth 提供者
const googleProvider = new GoogleAuthProvider()
googleProvider.addScope('email')
googleProvider.addScope('profile')

const facebookProvider = new FacebookAuthProvider()
facebookProvider.addScope('email')

const appleProvider = new OAuthProvider('apple.com')
appleProvider.addScope('email')
appleProvider.addScope('name')

// Electron 專用 OAuth 實現 - 使用系統瀏覽器 + localhost 回調
const openElectronOAuth = async (provider: 'google' | 'facebook' | 'apple'): Promise<any> => {
  return new Promise((resolve, reject) => {
    const { shell } = require('electron')
    const http = require('http')
    const crypto = require('crypto')
    const url = require('url')

    // 生成隨機 state 參數防止 CSRF 攻擊
    const state = crypto.randomBytes(32).toString('hex')

    // 找一個可用的端口
    const port = 8080 + Math.floor(Math.random() * 1000)

    // 創建本地 HTTP 服務器
    const server = http.createServer((req: any, res: any) => {
      const parsedUrl = url.parse(req.url, true)

      if (parsedUrl.pathname === '/callback') {
        const { code, error, state: returnedState } = parsedUrl.query

        // 發送成功頁面給瀏覽器
        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' })
        res.end(`
          <html>
            <head>
              <title>SpeakOneAI - 登入${error ? '失敗' : '成功'}</title>
              <style>
                body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }
                .container { max-width: 500px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                .success { color: #4CAF50; }
                .error { color: #f44336; }
                .countdown { font-size: 18px; color: #666; margin-top: 20px; }
              </style>
            </head>
            <body>
              <div class="container">
                <h1 class="${error ? 'error' : 'success'}">${error ? '❌ 登入失敗' : '✅ 登入成功'}</h1>
                <p>${error ? error : '正在處理登入資訊，請稍候...'}</p>
                ${!error ? '<div class="countdown">視窗將在 <span id="countdown">2</span> 秒後自動關閉</div>' : ''}
                <p><small>您也可以手動關閉此視窗並返回應用程式。</small></p>
              </div>
              <script>
                ${!error ? `
                  let countdown = 3;
                  const countdownElement = document.getElementById('countdown');

                  // 立即添加手動關閉按鈕
                  function addCloseButton() {
                    const container = document.querySelector('.container');
                    if (container && !document.getElementById('manual-close-btn')) {
                      const closeBtn = document.createElement('button');
                      closeBtn.id = 'manual-close-btn';
                      closeBtn.textContent = '立即關閉視窗';
                      closeBtn.style.cssText = 'margin-top: 15px; padding: 12px 24px; background: #4285f4; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 16px; font-weight: bold;';
                      closeBtn.onclick = () => {
                        console.log('手動關閉按鈕被點擊');
                        attemptClose();
                      };
                      container.appendChild(closeBtn);
                    }
                  }

                  // 嘗試關閉視窗的函數
                  function attemptClose() {
                    console.log('嘗試關閉視窗...');

                    // 方法 1: 直接關閉
                    try {
                      window.close();
                      console.log('window.close() 調用成功');
                      return;
                    } catch (e) {
                      console.log('window.close() 失敗:', e);
                    }

                    // 方法 2: 通過 opener 關閉
                    try {
                      if (window.opener && !window.opener.closed) {
                        window.opener.focus();
                        window.close();
                        console.log('通過 opener 關閉成功');
                        return;
                      }
                    } catch (e) {
                      console.log('通過 opener 關閉失敗:', e);
                    }

                    // 方法 3: 使用 history.back()
                    try {
                      if (window.history.length > 1) {
                        window.history.back();
                        console.log('使用 history.back()');
                        return;
                      }
                    } catch (e) {
                      console.log('history.back() 失敗:', e);
                    }

                    // 方法 4: 替換頁面內容為關閉指示
                    console.log('所有自動關閉方法都失敗，顯示手動關閉指示');
                    document.body.innerHTML = \`
                      <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif; background: #f5f5f5; min-height: 100vh; display: flex; flex-direction: column; justify-content: center; align-items: center;">
                        <div style="background: white; padding: 40px; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); max-width: 400px;">
                          <h2 style="color: #4285f4; margin-bottom: 20px;">✅ 登入成功！</h2>
                          <p style="color: #666; margin-bottom: 30px; line-height: 1.5;">
                            由於瀏覽器安全限制，無法自動關閉此視窗。<br>
                            請手動關閉此分頁並返回應用程式。
                          </p>
                          <button onclick="attemptClose()" style="padding: 12px 24px; font-size: 16px; background: #4285f4; color: white; border: none; border-radius: 6px; cursor: pointer; margin-right: 10px;">
                            再次嘗試關閉
                          </button>
                          <button onclick="window.location.href='about:blank'" style="padding: 12px 24px; font-size: 16px; background: #666; color: white; border: none; border-radius: 6px; cursor: pointer;">
                            清空頁面
                          </button>
                        </div>
                      </div>
                    \`;
                  }

                  // 倒數計時器
                  const timer = setInterval(() => {
                    countdown--;
                    if (countdownElement) {
                      countdownElement.textContent = countdown;
                    }

                    if (countdown <= 0) {
                      clearInterval(timer);
                      console.log('倒數計時結束，嘗試自動關閉');
                      attemptClose();
                    }
                  }, 1000);

                  // 頁面載入完成後立即添加按鈕
                  if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', addCloseButton);
                  } else {
                    addCloseButton();
                  }

                  // 監聽鍵盤事件 (ESC 鍵關閉)
                  document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape') {
                      console.log('ESC 鍵被按下，嘗試關閉視窗');
                      clearInterval(timer);
                      attemptClose();
                    }
                  });

                  // 監聽視窗焦點事件
                  window.addEventListener('focus', () => {
                    console.log('視窗獲得焦點');
                  });

                  window.addEventListener('blur', () => {
                    console.log('視窗失去焦點');
                  });

                ` : ''}
              </script>
            </body>
          </html>
        `)

        // 關閉服務器
        server.close()

        if (error) {
          reject(new Error(error))
          return
        }

        if (code && returnedState === state) {
          // 交換 authorization code 為 access token
          exchangeCodeForTokens(code, port).then(resolve).catch(reject)
        } else {
          reject(new Error('Invalid OAuth callback: missing code or state mismatch'))
        }
      } else {
        res.writeHead(404)
        res.end('Not Found')
      }
    })

    // 啟動服務器
    server.listen(port, 'localhost', () => {
      console.log(`🌐 OAuth callback server started on http://localhost:${port}`)

      // 構建 OAuth URL
      let authUrl: string

      if (provider === 'google') {
        const clientId = process.env.GOOGLE_CLIENT_ID || ''
        const redirectUri = encodeURIComponent(`http://localhost:${port}/callback`)
        authUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
          `client_id=${clientId}&` +
          `redirect_uri=${redirectUri}&` +
          `response_type=code&` +
          `scope=email%20profile&` +
          `state=${state}`
      } else {
        server.close()
        reject(new Error(`${provider} OAuth not implemented yet`))
        return
      }

      console.log(`🔐 Opening external browser for ${provider} OAuth:`, authUrl)

      // 打開外部瀏覽器
      shell.openExternal(authUrl)

      // 設置超時
      setTimeout(() => {
        server.close()
        reject(new Error('OAuth timeout - user did not complete authentication'))
      }, 300000) // 5 分鐘超時
    })

    server.on('error', (err: any) => {
      reject(new Error(`Failed to start OAuth server: ${err.message}`))
    })
  })
}

// 交換 authorization code 為 tokens
const exchangeCodeForTokens = async (code: string, port: number): Promise<any> => {
  try {
    const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: process.env.GOOGLE_CLIENT_ID || '',
        client_secret: process.env.GOOGLE_CLIENT_SECRET || '',
        code: code,
        grant_type: 'authorization_code',
        redirect_uri: `http://localhost:${port}/callback`
      })
    })

    const tokenData = await tokenResponse.json()

    if (tokenData.error) {
      throw new Error(tokenData.error_description || tokenData.error)
    }

    console.log('✅ Successfully exchanged code for tokens')

    // 返回 access token 和 id token
    return {
      accessToken: tokenData.access_token,
      idToken: tokenData.id_token
    }

  } catch (error) {
    console.error('❌ Token exchange failed:', error)
    throw error
  }
}

// 創建模擬用戶（用於測試）
const createMockUser = (provider: string, token: string): FirebaseUser => {
  const uid = `${provider}-user-${Date.now()}`
  const email = provider === 'google' ? '<EMAIL>' :
                provider === 'facebook' ? '<EMAIL>' :
                '<EMAIL>'

  return {
    uid,
    email,
    displayName: `${provider.charAt(0).toUpperCase() + provider.slice(1)} User`,
    photoURL: null,
    phoneNumber: null,
    providerId: `${provider}.com`,
    emailVerified: true,
    isAnonymous: false,
    metadata: {
      creationTime: new Date().toISOString(),
      lastSignInTime: new Date().toISOString()
    },
    providerData: [{
      providerId: `${provider}.com`,
      uid: `${provider}-${Date.now()}`,
      displayName: `${provider.charAt(0).toUpperCase() + provider.slice(1)} User`,
      email,
      photoURL: null,
      phoneNumber: null
    }],
    refreshToken: token,
    tenantId: null,
    delete: async () => {},
    getIdToken: async () => token,
    getIdTokenResult: async () => ({
      token,
      authTime: new Date().toISOString(),
      issuedAtTime: new Date().toISOString(),
      expirationTime: new Date(Date.now() + 3600000).toISOString(),
      signInProvider: `${provider}.com`,
      signInSecondFactor: null,
      claims: {
        email,
        name: `${provider.charAt(0).toUpperCase() + provider.slice(1)} User`,
        provider
      }
    }),
    reload: async () => {},
    toJSON: () => ({})
  } as FirebaseUser
}

export const signInWithGoogle = async (): Promise<FirebaseUser | null> => {
  try {
    console.log('🔐 Starting Google sign in for Electron...')

    // 檢查是否啟用 Mock 認證
    if (process.env.ENABLE_MOCK_AUTH === 'true') {
      console.log('🧪 Using mock authentication')
      return createMockUser('google', 'mock-google-token')
    }

    // Electron 環境：使用外部瀏覽器 + Deep Link OAuth 流程
    const tokens = await openElectronOAuth('google')

    // 使用 Google ID Token 創建 Firebase credential
    const credential = GoogleAuthProvider.credential(tokens.idToken, tokens.accessToken)

    console.log('🔐 Attempting Firebase sign in with credential...')
    console.log('🔧 Current Firebase config check:', {
      hasAuth: !!auth,
      authApp: auth.app.name,
      authConfig: auth.config
    })

    // 使用 credential 登入 Firebase
    const userCredential = await signInWithCredential(auth, credential)
    const user = userCredential.user

    console.log('✅ Google sign in successful:', user.email)

    // 驗證 token 並獲取用戶資訊
    try {
      // 動態導入 FirebaseFunctionLogger 以避免循環依賴
      const { FirebaseFunctionLogger } = await import('./src/main/utils/FirebaseFunctionLogger')
      await FirebaseFunctionLogger.call('verify_token')
      console.log('✅ Token verified')
    } catch (error) {
      console.warn('⚠️ Token verification failed, but login succeeded')
    }

    return user

  } catch (error) {
    console.error('❌ Google sign in error:', error)
    throw error
  }
}

export const signInWithFacebook = async (): Promise<FirebaseUser | null> => {
  try {
    console.log('🔐 Starting Facebook sign in for Electron...')

    // 檢查是否啟用 Mock 認證
    if (process.env.ENABLE_MOCK_AUTH === 'true') {
      console.log('🧪 Using mock authentication')
      return createMockUser('facebook', 'mock-facebook-token')
    }

    // Facebook OAuth 尚未實現
    throw new Error('Facebook OAuth not implemented yet for Electron')

  } catch (error) {
    console.error('❌ Facebook sign in error:', error)
    throw error
  }
}

export const signInWithApple = async (): Promise<FirebaseUser | null> => {
  try {
    console.log('🔐 Starting Apple sign in for Electron...')

    // 檢查是否啟用 Mock 認證
    if (process.env.ENABLE_MOCK_AUTH === 'true') {
      console.log('🧪 Using mock authentication')
      return createMockUser('apple', 'mock-apple-token')
    }

    // Apple OAuth 尚未實現
    throw new Error('Apple OAuth not implemented yet for Electron')

  } catch (error) {
    console.error('❌ Apple sign in error:', error)
    throw error
  }
}

// 登出函數
export const signOut = async (): Promise<void> => {
  try {
    await firebaseSignOut(auth)
    console.log('✅ Sign out successful')
  } catch (error) {
    console.error('❌ Sign out error:', error)
    throw error
  }
}

export const signOutUser = async (): Promise<void> => {
  try {
    await auth.signOut()
    console.log('用戶已登出')
  } catch (error) {
    console.error('登出失敗:', error)
    throw error
  }
}

export const getCurrentUser = (): FirebaseUser | null => {
  return auth.currentUser
}

export const getCurrentUserToken = async (): Promise<string | null> => {
  const user = auth.currentUser
  if (user) {
    try {
      return await user.getIdToken()
    } catch (error) {
      console.error('獲取 Token 失敗:', error)
      return null
    }
  }
  return null
}

export const onAuthStateChange = (callback: (user: FirebaseUser | null) => void) => {
  return auth.onAuthStateChanged(callback)
}

export const waitForAuthInit = (): Promise<FirebaseUser | null> => {
  return new Promise((resolve) => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      unsubscribe()
      resolve(user)
    })
  })
}

// 導出默認配置
export default {
  app,
  auth,
  db,
  functions,
  firebaseConfig
}
