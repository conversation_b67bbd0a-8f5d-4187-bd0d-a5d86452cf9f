// SpeechPilot 登入視窗 JavaScript

// 檢查是否在 Electron 環境中
const isElectron = typeof require !== 'undefined'
let ipc<PERSON>enderer = null

if (isElectron) {
    try {
        ipcRenderer = require('electron').ipcRenderer
    } catch (error) {
        console.error('Failed to load ipcRenderer:', error)
    }
}

// DOM 元素
const loadingOverlay = document.getElementById('loadingOverlay')
const errorMessage = document.getElementById('errorMessage')
const errorText = document.getElementById('errorText')
const loadingText = document.getElementById('loadingText')
const googleBtn = document.getElementById('googleBtn')
const facebookBtn = document.getElementById('facebookBtn')
const appleBtn = document.getElementById('appleBtn')

// 顯示載入狀態
function showLoading(provider) {
    const providerNames = {
        google: 'Google',
        facebook: 'Facebook',
        apple: 'Apple'
    }
    
    loadingText.textContent = `正在使用 ${providerNames[provider]} 登入...`
    loadingOverlay.style.display = 'flex'
    hideError()
    
    // 禁用所有按鈕
    googleBtn.disabled = true
    facebookBtn.disabled = true
    appleBtn.disabled = true
}

// 隱藏載入狀態
function hideLoading() {
    loadingOverlay.style.display = 'none'
    
    // 啟用所有按鈕
    googleBtn.disabled = false
    facebookBtn.disabled = false
    appleBtn.disabled = false
}

// 顯示錯誤訊息
function showError(message) {
    errorText.textContent = message
    errorMessage.style.display = 'block'
    hideLoading()
}

// 隱藏錯誤訊息
function hideError() {
    errorMessage.style.display = 'none'
}

// Google 登入
async function loginWithGoogle() {
    console.log('Google login clicked')
    
    if (!ipcRenderer) {
        showError('無法連接到應用程式，請重新啟動')
        return
    }
    
    try {
        showLoading('google')
        
        const result = await ipcRenderer.invoke('login-with-google')
        
        if (result.success) {
            console.log('Google login successful')
            // 登入成功，視窗會自動關閉
        } else {
            showError(result.error || 'Google 登入失敗')
        }
    } catch (error) {
        console.error('Google login error:', error)
        showError('登入過程中發生錯誤，請稍後再試')
    }
}

// Facebook 登入
async function loginWithFacebook() {
    console.log('Facebook login clicked')
    
    if (!ipcRenderer) {
        showError('無法連接到應用程式，請重新啟動')
        return
    }
    
    try {
        showLoading('facebook')
        
        const result = await ipcRenderer.invoke('login-with-facebook')
        
        if (result.success) {
            console.log('Facebook login successful')
            // 登入成功，視窗會自動關閉
        } else {
            showError(result.error || 'Facebook 登入失敗')
        }
    } catch (error) {
        console.error('Facebook login error:', error)
        showError('登入過程中發生錯誤，請稍後再試')
    }
}

// Apple 登入
async function loginWithApple() {
    console.log('Apple login clicked')
    
    if (!ipcRenderer) {
        showError('無法連接到應用程式，請重新啟動')
        return
    }
    
    try {
        showLoading('apple')
        
        const result = await ipcRenderer.invoke('login-with-apple')
        
        if (result.success) {
            console.log('Apple login successful')
            // 登入成功，視窗會自動關閉
        } else {
            showError(result.error || 'Apple 登入失敗')
        }
    } catch (error) {
        console.error('Apple login error:', error)
        showError('登入過程中發生錯誤，請稍後再試')
    }
}

// 關閉視窗
async function closeWindow() {
    console.log('Close window clicked')
    
    if (!ipcRenderer) {
        window.close()
        return
    }
    
    try {
        await ipcRenderer.invoke('close-login-window')
    } catch (error) {
        console.error('Close window error:', error)
        window.close()
    }
}

// 開啟服務條款
function openTerms() {
    console.log('Open terms clicked')
    // 這裡可以開啟服務條款頁面
    if (ipcRenderer) {
        // 可以發送事件到主進程開啟外部連結
        // ipcRenderer.send('open-external-url', 'https://your-terms-url.com')
    }
}

// 開啟隱私政策
function openPrivacy() {
    console.log('Open privacy clicked')
    // 這裡可以開啟隱私政策頁面
    if (ipcRenderer) {
        // 可以發送事件到主進程開啟外部連結
        // ipcRenderer.send('open-external-url', 'https://your-privacy-url.com')
    }
}

// 監聽登入狀態更新
if (ipcRenderer) {
    ipcRenderer.on('login-status-update', (event, status) => {
        console.log('Login status update:', status)
        
        if (status.isLoading) {
            showLoading(status.provider || 'unknown')
        } else {
            hideLoading()
        }
        
        if (status.error) {
            showError(status.error)
        }
    })
}

// 頁面載入完成後的初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('Login page loaded')
    
    // 隱藏載入狀態和錯誤訊息
    hideLoading()
    hideError()
    
    // 如果不在 Electron 環境中，顯示警告
    if (!isElectron) {
        showError('此頁面需要在 SpeechPilot 應用程式中運行')
        googleBtn.disabled = true
        facebookBtn.disabled = true
        appleBtn.disabled = true
    }
})

// 鍵盤事件處理
document.addEventListener('keydown', (event) => {
    // ESC 鍵關閉視窗
    if (event.key === 'Escape') {
        closeWindow()
    }
    
    // Enter 鍵預設使用 Google 登入
    if (event.key === 'Enter' && !googleBtn.disabled) {
        loginWithGoogle()
    }
})

// 暴露函數到全局作用域
window.loginWithGoogle = loginWithGoogle
window.loginWithFacebook = loginWithFacebook
window.loginWithApple = loginWithApple
window.closeWindow = closeWindow
window.openTerms = openTerms
window.openPrivacy = openPrivacy
