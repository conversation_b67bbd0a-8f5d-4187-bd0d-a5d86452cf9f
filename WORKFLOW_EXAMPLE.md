# SpeechPilot 工作流程使用示例

這個文件展示了如何在 SpeechPilot 應用程式中使用新的三個工作流程。

## 工作流程概述

### 1. 認證檢查 (Auth Check)
- 檢查用戶是否已登入
- 驗證訂閱狀態和可用使用時間
- 如果無可用時間，提示升級

### 2. 錄音前檢查 (Pre-Recording Check)
- 在開始錄音前檢查可用時間
- 如果時間不足，阻止錄音並提示升級
- 確保用戶有足夠時間完成錄音

### 3. 錄音後計算 (Post-Recording Calculation)
- 錄音完成後計算實際使用時間
- 記錄 tokens 使用量
- 更新用戶使用統計

## 代碼示例

### 在主程序中使用 ServiceManager

```typescript
import { getServiceManager } from './src/main/services/ServiceManager'

class SpeechPilotApp {
  private serviceManager = getServiceManager()

  async initialize() {
    // 初始化服務
    await this.serviceManager.initialize()
    
    // 監聽事件
    this.serviceManager.on('usage-limit-exceeded', (remainingSeconds) => {
      this.showUpgradeDialog(remainingSeconds)
    })
  }

  // 工作流程 1: 應用程式啟動時檢查認證狀態
  async checkAuthOnStartup() {
    const authStatus = await this.serviceManager.checkAuthAndSubscription()
    
    if (!authStatus.isAuthenticated) {
      // 顯示登入界面
      this.showLoginDialog()
      return
    }

    if (!authStatus.canUse) {
      // 顯示升級提示
      this.showUpgradeDialog(authStatus.availableSecondsToday)
      return
    }

    console.log(`歡迎回來！您今天還有 ${authStatus.availableSecondsToday} 秒可用時間`)
  }

  // 工作流程 2: 用戶按下錄音快捷鍵時
  async onRecordingHotkeyPressed() {
    // 預估錄音時間（例如 2 分鐘）
    const estimatedDuration = 120
    
    const checkResult = await this.serviceManager.checkBeforeRecording(estimatedDuration)
    
    if (!checkResult.canRecord) {
      if (checkResult.upgradeRequired) {
        // 顯示升級對話框
        this.showUpgradeDialog(checkResult.availableSeconds || 0, checkResult.upgradeUrl)
      } else {
        // 顯示錯誤訊息
        this.showErrorMessage(checkResult.reason || '無法開始錄音')
      }
      return
    }

    // 可以開始錄音
    await this.startRecording(['ai-speech-to-text'], estimatedDuration)
  }

  // 開始錄音
  async startRecording(features: string[], estimatedDuration: number) {
    try {
      // 開始使用會話
      const sessionStarted = await this.serviceManager.startUsageSession(features, estimatedDuration)
      
      if (!sessionStarted) {
        this.showErrorMessage('無法開始錄音會話')
        return
      }

      // 顯示錄音界面
      this.showRecordingWindow()
      
      // 開始實際錄音邏輯
      await this.startAudioRecording()
      
    } catch (error) {
      console.error('開始錄音失敗:', error)
      this.showErrorMessage('錄音啟動失敗')
    }
  }

  // 工作流程 3: 錄音完成時
  async onRecordingCompleted() {
    try {
      // 停止錄音
      await this.stopAudioRecording()
      
      // 結束使用會話並計算使用量
      const result = await this.serviceManager.endUsageSession()
      
      if (result.success) {
        console.log(`錄音完成！使用時間: ${result.secondsUsed}秒，Tokens: ${result.tokensUsed}`)
        
        // 顯示使用統計
        this.showUsageStats(result.secondsUsed, result.tokensUsed)
        
        // 處理錄音結果（轉文字、AI處理等）
        await this.processRecordingResult()
        
      } else {
        console.error('計算使用量失敗:', result.error)
        this.showErrorMessage('使用量記錄失敗，但錄音已完成')
      }
      
    } catch (error) {
      console.error('錄音完成處理失敗:', error)
    } finally {
      // 關閉錄音界面
      this.hideRecordingWindow()
    }
  }

  // 顯示升級對話框
  private showUpgradeDialog(remainingSeconds: number, upgradeUrl?: string) {
    const minutes = Math.floor(remainingSeconds / 60)
    const seconds = remainingSeconds % 60
    
    const message = remainingSeconds > 0 
      ? `您今天還剩 ${minutes}分${seconds}秒使用時間。升級以獲得更多使用時間！`
      : '您的使用時間已用完。升級以繼續使用 SpeechPilot！'
    
    // 顯示對話框，包含升級按鈕
    this.showDialog({
      title: '升級訂閱',
      message,
      buttons: [
        {
          text: '升級',
          action: () => {
            if (upgradeUrl) {
              // 打開升級網頁
              require('electron').shell.openExternal(upgradeUrl)
            }
          }
        },
        {
          text: '稍後',
          action: () => {
            // 關閉對話框
          }
        }
      ]
    })
  }

  // 顯示使用統計
  private showUsageStats(secondsUsed: number, tokensUsed?: number) {
    const minutes = Math.floor(secondsUsed / 60)
    const seconds = secondsUsed % 60
    
    let message = `本次錄音使用時間: ${minutes}分${seconds}秒`
    
    if (tokensUsed) {
      message += `\nTokens 使用量: ${tokensUsed}`
    }
    
    // 顯示通知或狀態欄訊息
    this.showNotification(message)
  }

  // 其他輔助方法...
  private showLoginDialog() { /* 實作登入界面 */ }
  private showRecordingWindow() { /* 實作錄音界面 */ }
  private hideRecordingWindow() { /* 隱藏錄音界面 */ }
  private startAudioRecording() { /* 實作音頻錄製 */ }
  private stopAudioRecording() { /* 停止音頻錄製 */ }
  private processRecordingResult() { /* 處理錄音結果 */ }
  private showErrorMessage(message: string) { /* 顯示錯誤訊息 */ }
  private showDialog(options: any) { /* 顯示對話框 */ }
  private showNotification(message: string) { /* 顯示通知 */ }
}
```

## 重要注意事項

### 1. 錯誤處理
- 所有工作流程都包含完整的錯誤處理
- 網路錯誤時應該有適當的重試機制
- 用戶體驗應該保持流暢

### 2. 用戶體驗
- 升級提示應該友善且不打擾
- 使用量統計應該清楚易懂
- 錯誤訊息應該具體且可操作

### 3. 安全性
- 所有使用量計算都在伺服器端進行
- 客戶端無法篡改使用時間
- 設備註冊確保帳戶安全

### 4. 效能
- 檢查操作應該快速完成
- 避免頻繁的 API 調用
- 適當的快取機制

## 測試建議

1. **測試不同訂閱計劃**：確保各個計劃的限制正確執行
2. **測試邊界情況**：時間用完、網路斷線等情況
3. **測試升級流程**：確保升級連結和流程正常
4. **測試使用量計算**：驗證時間和 tokens 計算準確性

這個工作流程設計確保了用戶體驗的流暢性，同時保護了業務邏輯的安全性。
