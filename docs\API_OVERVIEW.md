# 📊 SpeechPilot API 總覽

**完整的 API 架構和功能概述**

## 🌏 部署資訊

- **區域**: Asia East (asia-east1)
- **基礎 URL**: `https://asia-east1-speechpilot-f1495.cloudfunctions.net`
- **運行時**: Python 3.11
- **架構**: Firebase Functions v2
- **認證**: Firebase Authentication 必需

## 📋 API 函數列表

### 🔐 認證與用戶管理 (4 個函數)
| 函數名稱 | 功能 | 輸入參數 | 主要回應 |
|----------|------|----------|----------|
| `verify_token` | 驗證 API Token | - | `token_valid`, `user_id` |
| `create_or_update_user` | 創建或更新用戶 | `uid`, `email`, `name`, `platform` | `user`, `subscription` |
| `create_subscription` | 創建訂閱記錄 | `plan`, `source_app` | `subscription`, `is_new` |
| `check_user_subscription_status` | 檢查訂閱狀態 | - | `subscription`, `usage`, `limits` |

### 📱 設備管理 (3 個函數)
| 函數名稱 | 功能 | 輸入參數 | 主要回應 |
|----------|------|----------|----------|
| `register_device_v2` | 設備註冊（統一接口） | `device_id`, `platform`, `device_info` | `success`, `device_count`, `platform_supported` |
| `validate_device` | 設備存取驗證 | `device_id`, `platform` | `has_access`, `subscription_plan` |
| `get_devices_info` | 獲取設備資訊 | - | `devices[]`, `total_devices`, `max_devices` |

### 📈 使用量管理 (2 個函數)
| 函數名稱 | 功能 | 輸入參數 | 主要回應 |
|----------|------|----------|----------|
| `check_usage_before_recording` | 錄音前檢查 | `device_id` | `can_use`, `remaining_seconds` |
| `submit_usage` | 提交使用記錄 | `duration_seconds`, `feature_type` | `remaining_quota`, `daily_used` |

### 👤 用戶資訊 (1 個函數)
| 函數名稱 | 功能 | 輸入參數 | 主要回應 |
|----------|------|----------|----------|
| `get_user_info` | 獲取用戶資訊 | - | `user_data`, `usage_stats`, `plan_limits` |

## 📊 統計總結

- **總函數數量**: 10 個核心 HTTP Callable Functions
- **認證與用戶**: 4 個函數
- **設備管理**: 3 個函數
- **使用量管理**: 2 個函數
- **用戶資訊**: 1 個函數

## 🔄 API 調用工作流程

### 1. 新用戶登入流程
```mermaid
graph TD
    A[SSO 登入成功] --> B[create_or_update_user]
    B --> C[自動創建 FREE 訂閱]
    C --> D[check_user_subscription_status]
    D --> E[register_device_v2]
    E --> F[開始使用應用]
```

**詳細步驟**：
1. **SSO 登入** → 獲得 Firebase Auth Token
2. **create_or_update_user** → 創建用戶記錄，內部自動調用 `get_or_create_subscription` 創建 FREE 訂閱
3. **check_user_subscription_status** → 確認訂閱狀態和使用量
4. **register_device_v2** → 註冊設備（FREE 計劃最多 1 台設備）
5. **開始使用** → 用戶可以開始錄音

**新用戶檢測邏輯**：
- 檢查 `users` 表是否存在用戶記錄 **AND** `subscriptions` 表是否存在訂閱記錄
- 只有當兩個表都不存在記錄時，才視為新用戶
- 防止重複創建訂閱的競態條件

### 2. 現有用戶登入流程
```mermaid
graph TD
    A[SSO 登入成功] --> B[create_or_update_user]
    B --> C[更新最後登入時間]
    C --> D[check_user_subscription_status]
    D --> E{是否為新設備?}
    E -->|是| F{設備數量是否超限?}
    E -->|否| G[validate_device]
    F -->|超限| H[返回升級提示]
    F -->|未超限| I[register_device_v2]
    G --> J[開始使用應用]
    I --> J
    H --> K[阻止使用]
```

**詳細步驟**：
1. **SSO 登入** → 獲得 Firebase Auth Token
2. **create_or_update_user** → 更新用戶最後登入資訊
3. **check_user_subscription_status** → 檢查當前訂閱和使用量
4. **設備檢查**：
   - 如果是**新設備**且**超出設備限制**：
     ```json
     {
       "error": {
         "code": "functions/resource-exhausted",
         "message": "已達到設備數量限制 (1)",
         "details": {
           "upgrade_required": true,
           "current_plan": "FREE",
           "max_devices": 1
         }
       }
     }
     ```
     **前端處理**: 顯示升級提示，阻止使用
   - 如果是**新設備**且**未超限** → register_device_v2 註冊設備
   - 如果是**已註冊設備** → validate_device 驗證存取權限
5. **開始使用** → 用戶可以開始錄音

### 3. 錄音使用流程
```mermaid
graph TD
    A[用戶點擊錄音] --> B[check_usage_before_recording]
    B --> C{是否有剩餘時間?}
    C -->|否| D[顯示升級提示]
    C -->|是| E[開始錄音]
    E --> F[錄音完成]
    F --> G[submit_usage]
    G --> H[更新使用量統計]
```

**詳細步驟**：
1. **check_usage_before_recording** → 檢查今日剩餘使用時間
2. **如果無剩餘時間** → 返回升級提示，阻止錄音
3. **如果有剩餘時間** → 允許開始錄音
4. **錄音完成後** → submit_usage 提交實際使用時間
5. **更新統計** → 系統更新用戶的每日/每月使用量

## � 詳細 API 規格和範例

### `create_or_update_user` - 創建或更新用戶

#### 📥 請求參數
| 參數名稱 | 類型 | 必需 | 預設值 | 說明 |
|----------|------|------|--------|------|
| `uid` | `string` | ✅ | - | Firebase Auth 用戶 UID |
| `email` | `string` | ✅ | - | 用戶電子郵件地址 |
| `name` | `string` | ❌ | `""` | 用戶顯示名稱 |
| `image` | `string` | ❌ | `""` | 用戶頭像 URL |
| `auth_provider` | `string` | ❌ | `"google"` | 認證提供者：`"google"`, `"facebook"`, `"apple"`, `"email"` |
| `email_verified` | `boolean` | ❌ | `false` | 電子郵件是否已驗證 |
| `platform` | `string` | ❌ | `"windows"` | 平台：`"windows"`, `"macos"`, `"ios"`, `"android"`, `"linux"` |
| `app_version` | `string` | ❌ | `"1.0.0"` | 應用程式版本 |
| `source_app` | `string` | ❌ | `"desktop"` | 來源應用：`"desktop"`, `"mobile"`, `"web"` |

#### 📤 請求範例
```json
{
  "uid": "NyEr0mIa17X0nxjnHwJfDtJQbre2",
  "email": "<EMAIL>",
  "name": "John Doe",
  "auth_provider": "google",
  "email_verified": true,
  "platform": "windows",
  "app_version": "1.0.0",
  "source_app": "desktop"
}
```

#### 📤 回應格式
| 欄位名稱 | 類型 | 說明 |
|----------|------|------|
| `success` | `boolean` | 操作是否成功 |
| `data.user.id` | `string` | 用戶 UID |
| `data.user.email` | `string` | 用戶電子郵件 |
| `data.user.name` | `string` | 用戶顯示名稱 |
| `data.user.subscription_plan` | `string` | 當前訂閱計劃 |
| `data.user.is_active` | `boolean` | 帳戶是否啟用 |
| `data.user.is_new_user` | `boolean` | 是否為新用戶 |
| `data.subscription.plan` | `string` | 訂閱計劃名稱 |
| `data.subscription.status` | `string` | 訂閱狀態：`"active"`, `"inactive"`, `"cancelled"` |
| `data.subscription.daily_limit_seconds` | `number` | 每日使用限制（秒） |
| `data.subscription.max_devices` | `number` | 最大設備數量 |
| `message` | `string` | 操作結果訊息 |

#### 📤 回應範例
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "NyEr0mIa17X0nxjnHwJfDtJQbre2",
      "email": "<EMAIL>",
      "name": "John Doe",
      "subscription_plan": "FREE",
      "is_active": true,
      "is_new_user": true
    },
    "subscription": {
      "plan": "FREE",
      "status": "active",
      "daily_limit_seconds": 300,
      "max_devices": 1
    }
  },
  "message": "新用戶創建成功"
}
```

### `create_subscription` - 創建訂閱記錄

#### 📥 請求參數
| 參數名稱 | 類型 | 必需 | 預設值 | 說明 |
|----------|------|------|--------|------|
| `plan` | `string` | ❌ | `"FREE"` | 訂閱計劃：`"FREE"`, `"STARTER"`, `"PRO"`, `"PREMIUM"`, `"MAX"` |
| `user_id` | `string` | ❌ | 當前用戶 | 目標用戶 ID（目前只能為自己創建） |
| `source_app` | `string` | ❌ | `"desktop"` | 來源應用：`"desktop"`, `"mobile"`, `"web"` |
| `created_by` | `string` | ❌ | `"system"` | 創建者：`"system"`, `"user"`, `"admin"` |
| `stripe_subscription_id` | `string` | ❌ | `null` | Stripe 訂閱 ID（付費計劃） |
| `stripe_customer_id` | `string` | ❌ | `null` | Stripe 客戶 ID（付費計劃） |
| `expires_at` | `string` | ❌ | `null` | 到期時間 ISO 8601 格式（付費計劃） |
| `auto_renew` | `boolean` | ❌ | `true` | 是否自動續費（FREE 計劃固定為 false） |

#### 📤 請求範例
```json
{
  "plan": "FREE",
  "source_app": "desktop"
}
```

#### 📤 回應格式
| 欄位名稱 | 類型 | 說明 |
|----------|------|------|
| `success` | `boolean` | 操作是否成功 |
| `data.subscription.user_id` | `string` | 用戶 ID |
| `data.subscription.plan` | `string` | 訂閱計劃 |
| `data.subscription.status` | `string` | 訂閱狀態 |
| `data.subscription.daily_limit_seconds` | `number` | 每日限制（秒） |
| `data.subscription.monthly_limit_seconds` | `number` | 每月限制（秒） |
| `data.subscription.max_devices` | `number` | 最大設備數 |
| `data.subscription.supported_platforms` | `string[]` | 支援的平台列表 |
| `data.subscription.features` | `string[]` | 功能列表 |
| `data.subscription.expires_at` | `string\|null` | 到期時間（付費計劃） |
| `data.subscription.auto_renew` | `boolean` | 是否自動續費 |
| `data.is_new` | `boolean` | 是否為新創建的訂閱 |
| `message` | `string` | 操作結果訊息 |

#### 📤 回應範例 - 新訂閱
```json
{
  "success": true,
  "data": {
    "subscription": {
      "user_id": "NyEr0mIa17X0nxjnHwJfDtJQbre2",
      "plan": "FREE",
      "status": "active",
      "daily_limit_seconds": 300,
      "monthly_limit_seconds": 9000,
      "max_devices": 1,
      "supported_platforms": ["windows", "macos"],
      "features": ["基本語音轉文字", "每日 5 分鐘"],
      "created_at": "2025-07-26T10:30:00Z",
      "expires_at": null,
      "auto_renew": false
    },
    "is_new": true,
    "plan": "FREE",
    "daily_limit_seconds": 300,
    "max_devices": 1
  },
  "message": "FREE 訂閱創建成功"
}
```

**回應 - 已有訂閱**:
```json
{
  "success": true,
  "data": {
    "subscription": {
      "plan": "PRO",
      "status": "active"
    },
    "is_new": false,
    "message": "用戶已有現有訂閱"
  }
}
```

**支援的訂閱計劃**:
- `FREE` - 免費計劃（每日 5 分鐘）
- `STARTER` - 入門計劃（每日 1 小時）
- `PRO` - 專業計劃（每日 2 小時）
- `PREMIUM` - 高級計劃（每日 8 小時）
- `MAX` - 無限制計劃

### `check_user_subscription_status` - 檢查訂閱狀態

#### 📥 請求參數
此 API 不需要任何參數，會自動檢查當前認證用戶的訂閱狀態。

#### 📤 回應格式
| 欄位名稱 | 類型 | 說明 |
|----------|------|------|
| `success` | `boolean` | 操作是否成功 |
| `data.subscription.plan` | `string` | 訂閱計劃名稱 |
| `data.subscription.status` | `string` | 訂閱狀態 |
| `data.subscription.daily_limit_seconds` | `number` | 每日使用限制（秒） |
| `data.subscription.monthly_limit_seconds` | `number` | 每月使用限制（秒） |
| `data.subscription.max_devices` | `number` | 最大設備數量 |
| `data.subscription.supported_platforms` | `string[]` | 支援的平台列表 |
| `data.usage.today_used` | `number` | 今日已使用時間（秒） |
| `data.usage.monthly_used` | `number` | 本月已使用時間（秒） |
| `data.usage.daily_remaining` | `number` | 今日剩餘時間（秒） |
| `data.usage.monthly_remaining` | `number` | 本月剩餘時間（秒） |
| `data.limits.can_use` | `boolean` | 是否可以使用服務 |
| `data.limits.can_use_today` | `boolean` | 今日是否可以使用 |
| `data.limits.can_use_monthly` | `boolean` | 本月是否可以使用 |
| `data.limits.needs_upgrade` | `boolean` | 是否需要升級計劃 |
| `data.devices.current_count` | `number` | 當前設備數量 |
| `data.devices.max_devices` | `number` | 最大設備數量 |
| `data.devices.can_add_device` | `boolean` | 是否可以添加新設備 |

#### 📤 回應範例
```json
{
  "success": true,
  "data": {
    "subscription": {
      "plan": "FREE",
      "status": "active",
      "daily_limit_seconds": 300,
      "monthly_limit_seconds": 9000,
      "max_devices": 1,
      "supported_platforms": ["windows", "macos"]
    },
    "usage": {
      "today_used": 120,
      "monthly_used": 1200,
      "daily_remaining": 180,
      "monthly_remaining": 7800
    },
    "limits": {
      "can_use": true,
      "can_use_today": true,
      "can_use_monthly": true,
      "needs_upgrade": false
    },
    "devices": {
      "current_count": 1,
      "max_devices": 1,
      "can_add_device": false
    }
  }
}
```

### `register_device_v2` - 設備註冊

#### 📥 請求參數
| 參數名稱 | 類型 | 必需 | 預設值 | 說明 |
|----------|------|------|--------|------|
| `device_id` | `string` | ✅ | - | 設備唯一 ID（建議格式：`platform-randomstring`） |
| `device_name` | `string` | ❌ | 自動生成 | 設備顯示名稱 |
| `platform` | `string` | ✅ | - | 平台：`"windows"`, `"macos"`, `"ios"`, `"android"`, `"linux"` |
| `app_version` | `string` | ✅ | - | 應用程式版本 |
| `device_info` | `object` | ❌ | `{}` | 設備詳細資訊 |
| `device_info.os_version` | `string` | ❌ | - | 作業系統版本 |
| `device_info.fingerprint` | `string` | ❌ | - | 設備指紋（用於識別） |
| `device_info.screen_resolution` | `string` | ❌ | - | 螢幕解析度（如：`"1920x1080"`） |
| `device_info.user_agent` | `string` | ❌ | - | 瀏覽器 User Agent（網頁版） |

#### 📤 請求範例
```json
{
  "device_id": "desktop-abc123",
  "device_name": "Windows Device",
  "platform": "windows",
  "app_version": "1.0.0",
  "device_info": {
    "os_version": "10.0.26100",
    "fingerprint": "c8bc31372f25d952",
    "screen_resolution": "1920x1080"
  }
}
```

#### 📤 回應格式
| 欄位名稱 | 類型 | 說明 |
|----------|------|------|
| `success` | `boolean` | 操作是否成功 |
| `data.device_id` | `string` | 設備 ID |
| `data.is_new` | `boolean` | 是否為新註冊的設備 |
| `data.platform_supported` | `boolean` | 平台是否被當前訂閱支援 |
| `data.device_count` | `number` | 用戶當前設備總數 |
| `data.max_devices` | `number` | 訂閱計劃允許的最大設備數 |
| `data.subscription_plan` | `string` | 當前訂閱計劃 |
| `message` | `string` | 操作結果訊息 |

#### 📤 回應範例 - 成功
```json
{
  "success": true,
  "data": {
    "device_id": "desktop-abc123",
    "is_new": true,
    "platform_supported": true,
    "device_count": 1,
    "max_devices": 1,
    "subscription_plan": "FREE"
  },
  "message": "設備註冊成功"
}
```

#### 📤 錯誤回應 - 設備數量超限
```json
{
  "success": false,
  "error": {
    "code": "functions/resource-exhausted",
    "message": "已達到設備數量限制 (1)",
    "details": {
      "current_plan": "FREE",
      "max_devices": 1,
      "current_device_count": 1,
      "upgrade_required": true
    }
  }
}
```

#### 📤 錯誤回應 - 平台不支援
```json
{
  "success": false,
  "error": {
    "code": "functions/permission-denied",
    "message": "當前計劃 (FREE) 不支援 ios 平台",
    "details": {
      "current_plan": "FREE",
      "supported_platforms": ["windows", "macos"],
      "requested_platform": "ios"
    }
  }
}
```

### `validate_device` - 設備驗證

#### 📥 請求參數
| 參數名稱 | 類型 | 必需 | 預設值 | 說明 |
|----------|------|------|--------|------|
| `device_id` | `string` | ✅ | - | 要驗證的設備 ID |
| `platform` | `string` | ✅ | - | 設備平台 |

#### 📤 請求範例
```json
{
  "device_id": "desktop-abc123",
  "platform": "windows"
}
```

#### 📤 回應格式
| 欄位名稱 | 類型 | 說明 |
|----------|------|------|
| `success` | `boolean` | 操作是否成功 |
| `data.has_access` | `boolean` | 設備是否有存取權限 |
| `data.device_id` | `string` | 設備 ID |
| `data.subscription_plan` | `string` | 當前訂閱計劃 |
| `data.daily_limit_seconds` | `number` | 每日使用限制 |
| `data.max_devices` | `number` | 最大設備數量 |
| `message` | `string` | 操作結果訊息 |

#### 📤 回應範例 - 成功
```json
{
  "success": true,
  "data": {
    "has_access": true,
    "device_id": "desktop-abc123",
    "subscription_plan": "FREE",
    "daily_limit_seconds": 300,
    "max_devices": 1
  },
  "message": "設備驗證成功"
}
```

**回應 - 設備未註冊**:
```json
{
  "success": false,
  "has_access": false,
  "reason": "DEVICE_NOT_REGISTERED",
  "message": "設備未註冊"
}
```

### `check_usage_before_recording` - 錄音前檢查

#### 📥 請求參數
| 參數名稱 | 類型 | 必需 | 預設值 | 說明 |
|----------|------|------|--------|------|
| `device_id` | `string` | ✅ | - | 設備 ID |
| `features` | `string[]` | ❌ | `["ai-speech-to-text"]` | 要使用的功能列表 |

#### 📤 請求範例
```json
{
  "device_id": "desktop-abc123",
  "features": ["ai-speech-to-text"]
}
```

#### 📤 回應格式
| 欄位名稱 | 類型 | 說明 |
|----------|------|------|
| `success` | `boolean` | 操作是否成功 |
| `can_use` | `boolean` | 是否可以開始錄音 |
| `remaining_seconds` | `number` | 今日剩餘使用時間（秒） |
| `daily_limit` | `number` | 每日使用限制（秒） |
| `today_used` | `number` | 今日已使用時間（秒） |
| `reason` | `string` | 不能使用的原因（當 can_use 為 false 時） |
| `upgrade_required` | `boolean` | 是否需要升級計劃 |

#### 📤 回應範例 - 可以使用
```json
{
  "success": true,
  "can_use": true,
  "remaining_seconds": 180,
  "daily_limit": 300,
  "today_used": 120
}
```

**回應 - 使用量已達上限**:
```json
{
  "success": true,
  "can_use": false,
  "remaining_seconds": 0,
  "reason": "使用時間已用完",
  "upgrade_required": true,
  "upgrade_url": "https://speechpilot.com/upgrade"
}
```

### `submit_usage` - 提交使用記錄

#### 📥 請求參數
| 參數名稱 | 類型 | 必需 | 預設值 | 說明 |
|----------|------|------|--------|------|
| `duration_seconds` | `number` | ✅ | - | 使用時長（秒），必須 > 0 |
| `feature_type` | `string` | ❌ | `"ai-speech-to-text"` | 功能類型 |
| `device_id` | `string` | ✅ | - | 設備 ID |
| `session_metadata` | `object` | ❌ | `{}` | 會話元數據 |
| `session_metadata.audio_length` | `number` | ❌ | - | 音頻長度（秒） |
| `session_metadata.words_count` | `number` | ❌ | - | 轉換的字數 |
| `session_metadata.language` | `string` | ❌ | - | 語言代碼（如：`"zh-TW"`, `"en-US"`） |

#### 📤 請求範例
```json
{
  "duration_seconds": 60,
  "feature_type": "ai-speech-to-text",
  "device_id": "desktop-abc123",
  "session_metadata": {
    "audio_length": 58.5,
    "words_count": 120,
    "language": "zh-TW"
  }
}
```

#### 📤 回應格式
| 欄位名稱 | 類型 | 說明 |
|----------|------|------|
| `success` | `boolean` | 操作是否成功 |
| `usage_log_id` | `string` | 使用記錄 ID |
| `seconds_logged` | `number` | 實際記錄的使用時間（秒） |
| `remaining_seconds` | `number` | 今日剩餘使用時間（秒） |
| `daily_used` | `number` | 今日總使用時間（秒） |
| `monthly_used` | `number` | 本月總使用時間（秒） |

#### 📤 回應範例
```json
{
  "success": true,
  "usage_log_id": "log-uuid-123",
  "seconds_logged": 60,
  "remaining_seconds": 120,
  "daily_used": 180,
  "monthly_used": 1800
}
```

### `verify_token` - 驗證 API Token

#### 📥 請求參數
此 API 不需要任何參數，會自動驗證當前請求的 Firebase Auth Token。

#### 📤 回應格式
| 欄位名稱 | 類型 | 說明 |
|----------|------|------|
| `success` | `boolean` | 操作是否成功 |
| `token_valid` | `boolean` | Token 是否有效 |
| `user_id` | `string` | 用戶 UID |
| `expires_at` | `string` | Token 到期時間 |

#### 📤 回應範例
```json
{
  "success": true,
  "token_valid": true,
  "user_id": "NyEr0mIa17X0nxjnHwJfDtJQbre2",
  "expires_at": "2025-07-27T10:30:00Z"
}
```

### `get_user_info` - 獲取用戶資訊

#### 📥 請求參數
此 API 不需要任何參數，會自動獲取當前認證用戶的資訊。

#### 📤 回應格式
| 欄位名稱 | 類型 | 說明 |
|----------|------|------|
| `success` | `boolean` | 操作是否成功 |
| `data.user` | `object` | 用戶基本資訊 |
| `data.subscription` | `object` | 訂閱資訊 |
| `data.usage` | `object` | 使用量統計 |

#### 📤 回應範例
```json
{
  "success": true,
  "data": {
    "user": {
      "uid": "NyEr0mIa17X0nxjnHwJfDtJQbre2",
      "email": "<EMAIL>",
      "name": "John Doe",
      "subscription_plan": "FREE",
      "is_active": true
    },
    "subscription": {
      "plan": "FREE",
      "status": "active",
      "daily_limit_seconds": 300
    },
    "usage": {
      "today_used": 120,
      "remaining_today": 180
    }
  }
}
```

### `get_devices_info` - 獲取設備資訊

#### 📥 請求參數
此 API 不需要任何參數，會自動獲取當前用戶的所有設備資訊。

#### 📤 回應格式
| 欄位名稱 | 類型 | 說明 |
|----------|------|------|
| `success` | `boolean` | 操作是否成功 |
| `devices` | `object[]` | 設備列表 |
| `devices[].device_id` | `string` | 設備 ID |
| `devices[].device_name` | `string` | 設備名稱 |
| `devices[].platform` | `string` | 設備平台 |
| `devices[].is_active` | `boolean` | 是否啟用 |
| `devices[].last_active_at` | `string` | 最後活躍時間 |
| `total_devices` | `number` | 總設備數量 |
| `max_devices` | `number` | 最大設備數量 |

#### 📤 回應範例
```json
{
  "success": true,
  "devices": [
    {
      "device_id": "desktop-abc123",
      "device_name": "Windows Device",
      "platform": "windows",
      "is_active": true,
      "last_active_at": "2025-07-26T10:30:00Z"
    }
  ],
  "total_devices": 1,
  "max_devices": 1
}
```

## 🚨 重要變更記錄

### 2025-07-26 - API 簡化和優化
- ❌ **移除**: `calculate_usage_after_recording`
  - **原因**: 功能與 `submit_usage` 重複
  - **替代方案**: 直接使用 `submit_usage` 提交使用時間
  - **優勢**: 減少複雜的 session 管理和 token 計算

- ❌ **移除**: `end_session`
  - **原因**: 會話管理過於複雜，實際用途有限
  - **替代方案**: 設備的 `last_active_at` 已足夠追蹤活動
  - **優勢**: 簡化前端邏輯，減少不必要的 API 調用

- ✅ **新增**: `create_or_update_user`
  - **功能**: 統一用戶創建和更新邏輯
  - **自動功能**: 新用戶自動獲得 FREE 訂閱（每日 5 分鐘）

- ✅ **新增**: `create_subscription`
  - **功能**: 通用訂閱創建 API，支援所有計劃（FREE, STARTER, PRO, PREMIUM, MAX）
  - **多應用支援**: 可被桌面、移動、網站應用調用
  - **防重複**: 自動檢測現有訂閱，防止重複創建
  - **使用場景**:
    - 新用戶註冊時創建 FREE 訂閱
    - 用戶升級時創建付費訂閱
    - 管理員為用戶分配特定計劃

- ✅ **優化**: 簡化工作流程
  - **新用戶**: 3 步完成（創建用戶 → 檢查訂閱 → 註冊設備）
  - **現有用戶**: 智能設備檢查和限制處理
  - **錄音流程**: 2 步完成（檢查使用量 → 提交使用量）

## 🔧 客戶端配置要求

### JavaScript/TypeScript
```javascript
import { getFunctions } from 'firebase/functions';

// 必須指定 asia-east1 區域
const functions = getFunctions(app, 'asia-east1');
```

### Swift (iOS)
```swift
// 必須指定 asia-east1 區域
let functions = Functions.functions(region: "asia-east1")
```

### Kotlin (Android)
```kotlin
// 必須指定 asia-east1 區域
val functions = Firebase.functions("asia-east1")
```

## 📱 訂閱計劃詳細資訊

| 訂閱計劃 | 每日限制 | 每月限制 | Windows | macOS | iOS | Android | 最大設備數 | 價格 |
|----------|----------|----------|---------|-------|-----|---------|------------|------|
| **FREE** | 5 分鐘 | 150 分鐘 | ✅ | ✅ | ❌ | ❌ | 1 | 免費 |
| **STARTER** | 1 小時 | 30 小時 | ✅ | ✅ | ❌ | ❌ | 1 | $9.99/月 |
| **PRO** | 2 小時 | 60 小時 | ✅ | ✅ | ✅ | ✅ | 2 | $19.99/月 |
| **PREMIUM** | 8 小時 | 240 小時 | ✅ | ✅ | ✅ | ✅ | 5 | $59.99/月 |
| **MAX** | 無限制 | 無限制 | ✅ | ✅ | ✅ | ✅ | 無限制 | $129.99/月 |

### 🆓 FREE 計劃特色
- **自動分配**: 新用戶註冊時自動獲得
- **每日重置**: 每天 00:00 UTC 重置使用量
- **平台限制**: 僅支援桌面平台（Windows, macOS）
- **設備限制**: 最多 1 台設備
- **升級提示**: 當使用量或設備數達到限制時，系統會提示升級

## 🔐 安全性特性

- **Firebase Authentication**: 所有 API 端點都需要有效的認證 Token
- **用戶隔離**: 每個用戶只能存取自己的資料
- **設備驗證**: 嚴格的設備註冊和驗證流程
- **使用量限制**: 基於訂閱計劃的使用量控制
- **會話管理**: 防止同時使用設備數量超限

## 📈 性能指標

- **回應時間**: 平均 130ms (Asia East 區域)
- **可用性**: 99.9% SLA
- **並發支援**: 每個用戶最多 2 個同時會話（PREMIUM 計劃）
- **資料一致性**: 強一致性 (Firestore)

## 📚 相關文檔

- **[API_INTERFACE.md](API_INTERFACE.md)** - 詳細的 API 整合指南
- **[FUNCTION_SPEC.md](FUNCTION_SPEC.md)** - 完整的技術規格
- **[DEPLOYMENT_ASIA_EAST.md](DEPLOYMENT_ASIA_EAST.md)** - 部署總結
- **[FRONTEND_HANDOFF.md](FRONTEND_HANDOFF.md)** - 前端交接文檔

## 🎯 下一步建議

### 短期 (1-2 週)
1. **前端整合**: 使用 `API_INTERFACE.md` 進行客戶端開發
2. **測試驗證**: 完整的端到端測試
3. **錯誤處理**: 實作完善的錯誤處理機制

### 中期 (1-2 個月)
1. **監控設置**: 配置 Cloud Monitoring 和 Alerting
2. **性能優化**: 基於使用數據進行優化
3. **功能擴展**: 根據用戶反饋添加新功能

### 長期 (3-6 個月)
1. **多區域部署**: 考慮其他區域的部署
2. **API 版本控制**: 建立 API 版本管理策略
3. **自動化測試**: 建立 CI/CD 管道

## 🔧 前端調用範例

### JavaScript/TypeScript 完整範例
```javascript
import { getFunctions, httpsCallable } from 'firebase/functions';

// 初始化 Firebase Functions（重要：指定 asia-east1 區域）
const functions = getFunctions(app, 'asia-east1');

class SpeechPilotAPI {
  constructor() {
    this.functions = functions;
  }

  // 創建或更新用戶
  async createOrUpdateUser(userData) {
    const createUser = httpsCallable(this.functions, 'create_or_update_user');
    return await createUser(userData);
  }

  // 創建訂閱
  async createSubscription(plan = 'FREE', sourceApp = 'desktop') {
    const createSub = httpsCallable(this.functions, 'create_subscription');
    return await createSub({ plan, source_app: sourceApp });
  }

  // 註冊設備
  async registerDevice(deviceData) {
    const registerDevice = httpsCallable(this.functions, 'register_device_v2');
    return await registerDevice(deviceData);
  }

  // 檢查使用量
  async checkUsageBeforeRecording(deviceId) {
    const checkUsage = httpsCallable(this.functions, 'check_usage_before_recording');
    return await checkUsage({ device_id: deviceId });
  }

  // 提交使用記錄
  async submitUsage(durationSeconds, deviceId, metadata = {}) {
    const submitUsage = httpsCallable(this.functions, 'submit_usage');
    return await submitUsage({
      duration_seconds: durationSeconds,
      device_id: deviceId,
      feature_type: 'ai-speech-to-text',
      session_metadata: metadata
    });
  }

  // 檢查訂閱狀態
  async checkSubscriptionStatus() {
    const checkStatus = httpsCallable(this.functions, 'check_user_subscription_status');
    return await checkStatus();
  }
}

// 使用範例
const api = new SpeechPilotAPI();

// 完整的用戶註冊和設備註冊流程
async function initializeUser(user) {
  try {
    // 1. 創建或更新用戶
    const userResult = await api.createOrUpdateUser({
      uid: user.uid,
      email: user.email,
      name: user.displayName,
      auth_provider: 'google',
      email_verified: user.emailVerified,
      platform: 'windows',
      app_version: '1.0.0'
    });

    // 2. 註冊設備
    const deviceResult = await api.registerDevice({
      device_id: 'desktop-' + Math.random().toString(36).substr(2, 9),
      device_name: 'Windows Device',
      platform: 'windows',
      app_version: '1.0.0',
      device_info: {
        os_version: '10.0.26100',
        fingerprint: 'device-fingerprint'
      }
    });

    console.log('初始化完成:', { user: userResult.data, device: deviceResult.data });
    return true;
  } catch (error) {
    console.error('初始化失敗:', error);
    return false;
  }
}
```

### 錯誤處理範例
```javascript
async function handleAPICall(apiFunction) {
  try {
    const result = await apiFunction();
    return { success: true, data: result.data };
  } catch (error) {
    switch (error.code) {
      case 'functions/unauthenticated':
        alert('請先登入');
        break;
      case 'functions/permission-denied':
        alert('當前計劃不支援此功能，請升級');
        break;
      case 'functions/resource-exhausted':
        alert('已達到使用限制，請升級計劃');
        break;
      default:
        alert(`操作失敗: ${error.message}`);
    }
    return { success: false, error: error.message };
  }
}
```

這份總覽提供了 SpeechPilot API 的完整架構視圖，包含了前端團隊所需的所有資訊：參數規格、資料類型、回應格式和完整的調用範例。
