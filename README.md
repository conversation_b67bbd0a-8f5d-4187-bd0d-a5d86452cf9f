# SpeechPilot Desktop Client

A powerful speech-to-text desktop application for Windows and macOS with AI-powered features and secure cloud synchronization.

## Rules
This repo is for frontend team only, backend functions are handled by backend team. frontend team is prohibited to create any backend firebase function code.


## 🚀 Features

- **🎤 Speech-to-Text** - High-accuracy speech recognition with real-time processing
- **🤖 AI-Powered Features** - Advanced AI processing for enhanced accuracy
- **🔐 SSO Authentication** - Secure login with Google, Facebook, Apple
- **📊 Usage Tracking** - Secure server-side usage monitoring and limits
- **🔄 Cross-Platform Sync** - Seamless experience across Windows and macOS
- **⚡ Real-time Processing** - Low-latency speech processing
- **🛡️ Security First** - End-to-end encryption and secure data handling

## 🛠️ Tech Stack

- **Framework**: Electron with TypeScript
- **Authentication**: Firebase Auth with SSO providers
- **Database**: Firestore with security rules
- **Backend**: Firebase Functions (Python) - *separate repository*
- **UI**: Modern desktop interface with React
- **Security**: Client-side encryption and server-side validation

## 🏗️ SpeechPilot Ecosystem

The SpeechPilot ecosystem consists of multiple applications working together:

### 1. 🖥️ Desktop Client (This Repository)
- Windows & macOS applications
- Core speech-to-text features
- AI-powered processing
- Secure authentication and usage tracking

### 2. 📱 Mobile Apps (Separate Repository)
- iOS & Android applications
- Mobile-optimized speech recognition
- Cross-platform synchronization

### 3. 🌐 Web Portal ([SpeechPilot Web Repository](https://github.com/your-username/speechpilot-web))
- Account management and billing
- Subscription purchases via Stripe
- Usage analytics and reporting
- Download links for desktop/mobile apps

### 4. ⚡ Backend Functions (Separate Repository - Python Recommended)
- Firebase Functions for secure operations
- Usage validation and tracking
- Authentication middleware
- Database operations

### Data Flow
```
Desktop/Mobile Apps → Firebase Functions → Firestore
                 ↓
            Web Portal (Account Management)
                 ↓
            Stripe (Payment Processing)
```

---

## 中文說明 / Chinese Documentation

### SpeechPilot - AI語音助手

SpeechPilot是一個桌面應用程式，讓你可以在任何應用程式中使用語音輸入和AI指令處理。只需按下快捷鍵，說出你想要的內容，AI就會理解並自動輸入到當前的文字欄位中。 Speech in, text out.

#### 兩個模式:
1. **AI Speech-to-Text** - 用戶說話, AI理解後根據指示提供回應並自動輸入到當前的文字欄位中
   - 例如: 用戶說: 幫我寫一篇100字的LINKEDIN POST關於得獎感受。AI會理解這是寫作任務，並自動生成文章並輸入到當前的文字欄位中
   - gpt-4o-mini-audio-preview模型能直接進行整個流程

2. **Direct Speech-to-Text** - 用戶說話, AI直接把文字輸入到當前的文字欄位中
   - 例如: 用戶說: 我的名字是John。AI會直接輸入 "我的名字是John" 到當前的文字欄位中
   - azure speech service就足以進行整個流程

## 📊 Subscription Plans

| Plan | Daily Limit | Devices | Platforms | Features |
|------|-------------|---------|-----------|----------|
| Free | 5 minutes | 1 | Windows, macOS | Basic features |
| Starter | 1 hour | 1 | Windows, macOS | Priority support |
| Pro | 2 hours | 2 | All platforms | Advanced features |
| Premium | 8 hours | 5 | All platforms | All features + API |
| Max | Unlimited | Unlimited | All platforms | Enterprise features |

*Payment processing handled by [SpeechPilot Web Portal](https://github.com/your-username/speechpilot-web)*

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- Firebase project with Authentication and Firestore
- Firebase Functions deployed (separate repo recommended)

### Installation
```bash
git clone https://github.com/your-username/speechpilot-desktop.git
cd speechpilot-desktop
npm install
```

### Environment Setup
Create a `.env` file:
```env
FIREBASE_API_KEY=your_api_key
FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_STORAGE_BUCKET=your_project.appspot.com
FIREBASE_MESSAGING_SENDER_ID=your_sender_id
FIREBASE_APP_ID=your_app_id
```

### Development
```bash
npm run dev          # Start development
npm run build        # Build for production
npm run dist         # Package for distribution
```

## 📁 Project Structure

```
src/
├── main/                   # Electron main process
│   ├── services/          # Core services
│   │   ├── AuthService.ts        # Authentication management
│   │   ├── UsageTrackingService.ts # Usage tracking
│   │   └── ServiceManager.ts     # Service orchestration
│   └── config/            # Configuration files
│       └── SecurityConfig.ts     # Security settings
├── renderer/              # Electron renderer process
└── shared/                # Shared utilities

firebase.ts                # Firebase configuration
subscription-plans.ts      # Subscription plan definitions
db.ts                     # Database schemas and operations
functions-backup-for-separate-repo/ # Backup for separate repo
```

## 🔒 Security Features

- **Server-side Validation**: All usage tracking validated by Firebase Functions
- **Device Fingerprinting**: Prevents device spoofing and unauthorized access
- **Time Validation**: Clock skew detection and session duration limits
- **Rate Limiting**: API abuse prevention
- **Data Encryption**: Sensitive data hashing and masking
- **Token Management**: Automatic refresh and secure storage

## 🔗 Related Repositories

- **[SpeechPilot Web](https://github.com/your-username/speechpilot-web)** - Web portal for account management and billing
- **[SpeechPilot Functions](https://github.com/your-username/speechpilot-functions)** - Backend Firebase Functions (Python recommended)
- **[SpeechPilot Mobile](https://github.com/your-username/speechpilot-mobile)** - iOS & Android applications

---

## 中文功能特色

- 🎤 **全域語音錄製**: 在任何應用程式中按下快捷鍵即可開始錄音，使用 Web Audio API 進行高品質音頻捕獲
- 🤖 **AI指令理解**: 整合 Azure OpenAI gpt-4o-mini-audio-preview 模型，不只是語音轉文字，還能理解複雜指令如"寫一篇100字的文章"
- 📝 **精確語音識別**: 使用 Azure Speech Service 提供高精度的語音轉文字功能
- ⌨️ **智能文字輸入**: 使用 nut.js 實現真正的自動文字輸入，支援剪貼簿備用模式
- 🔧 **完整配置管理**: 在設定面板中安全配置 API 金鑰和自定義快捷鍵
- 🚨 **智能錯誤處理**: 完整的錯誤處理機制和用戶友好的通知系統
- 🌐 **跨平台支援**: 支援Windows和macOS
- ⚡ **現代化架構**: 使用 Electron + Vite + React + TypeScript 提供快速開發體驗
- 🎨 **美觀介面**: React 組件化設計，支援 Framer Motion 動畫和互動效果

## 使用說明

### 快捷鍵操作
- **Ctrl+Shift+C**: 以AI SPEECH TO TEXT模式馬上開始錄音, 彈出開始錄音視窗, 並開始錄製音頻, AI會理解你的語音內容並根據需求生成回應後輸入到當前的文字欄位中, 再按一次快捷鍵或點擊停止錄音按鈕停止錄音
- **Ctrl+Shift+V**: 以DIRECT SPEECH TO TEXT模式馬上開始錄音, 彈出開始錄音視窗, 並開始錄製音頻,並直接把文字輸入到當前的文字欄位中, 再按一次快捷鍵或點擊停止錄音按鈕停止錄音

### 開始錄音視窗
- 錄音視窗會顯示錄音狀態和進度
- 錄音過程中，再次按下快捷鍵或點擊停止錄音按鈕即可停止錄音
- 錄音完成後，會顯示處理狀態和結果
- 錄音完成後，會自動關閉錄音視窗

## 技術架構

### 項目結構
```
src/
├── main/
│   ├── main.ts              # 主進程入口
│   ├── services/            # 服務層
│   │   ├── AudioService.ts  # 音頻錄製服務
│   │   ├── AIService.ts     # AI 處理服務
│   │   └── TextInputService.ts # 文字輸入服務
│   ├── windows/             # 窗口管理
│   │   └── RecordingWindow.ts # 錄音窗口
│   └── renderer/            # 渲染器資源
│       └── recording.html   # 錄音窗口 HTML
```

### 重構亮點
- ✅ **模組化架構**: 將原本 492 行的單一文件重構為多個專門的服務模組
- ✅ **服務分離**: AudioService、AIService、TextInputService 各司其職
- ✅ **窗口管理**: RecordingWindow 專門處理錄音界面
- ✅ **代碼復用**: 服務可以在不同場景下重複使用
- ✅ **易於維護**: 每個模組職責單一，便於測試和維護

### HIGH LEVEL DESIGN
- Standard Best Practice TSX VITE REACT FRAMEWORK
- I expect to have a tidy and well-structured code-base

### SETTING MENU
- AUDIO DEVICE SELECTION
- START APP WHEN OS START
- HOTKEY MODE (TOGGLE OR HOLD)
- SHORTCUT KEYS
- MULTI-SELECTION FOR USER SELECT USUAL LANGUAGE THEY SPEAK
- SET TIMEOUT SECOND WHEN RECORDING STOP