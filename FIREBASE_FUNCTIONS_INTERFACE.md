# Firebase Functions 接口規範

## 🌐 部署後的 Functions URL

部署完成後，您的 Firebase Functions 將可通過以下 URL 訪問：

```
基礎 URL: https://us-central1-{your-project-id}.cloudfunctions.net
```

## 📋 必需的 Functions 列表

### 1. 認證相關 Functions

#### `verifyToken`
- **URL**: `https://us-central1-{project-id}.cloudfunctions.net/verifyToken`
- **方法**: POST (Firebase Callable)
- **用途**: 驗證用戶 Token
- **輸入**: 自動從 Firebase Auth 獲取
- **輸出**: `{ success: boolean, message?: string }`

#### `getUserInfo`
- **URL**: `https://us-central1-{project-id}.cloudfunctions.net/getUserInfo`
- **方法**: POST (Firebase Callable)
- **用途**: 獲取用戶詳細資訊
- **輸入**: 無（從認證 Token 獲取用戶 ID）
- **輸出**: `UserInfo` 對象

### 2. 工作流程 Functions

#### `checkUserSubscriptionStatus`
- **URL**: `https://us-central1-{project-id}.cloudfunctions.net/checkUserSubscriptionStatus`
- **方法**: POST (Firebase Callable)
- **用途**: 工作流程 1 - 檢查用戶訂閱狀態
- **輸入**: 無
- **輸出**:
```typescript
{
  canUse: boolean
  availableSecondsToday: number
  availableSecondsThisMonth: number
  needsUpgrade: boolean
  upgradeUrl?: string
  plan: string
}
```

#### `checkUsageBeforeRecording`
- **URL**: `https://us-central1-{project-id}.cloudfunctions.net/checkUsageBeforeRecording`
- **方法**: POST (Firebase Callable)
- **用途**: 工作流程 2 - 錄音前檢查
- **輸入**:
```typescript
{
  estimatedDurationSeconds: number
}
```
- **輸出**:
```typescript
{
  canStart: boolean
  availableSeconds: number
  message?: string
  upgradeUrl?: string
}
```

#### `calculateUsageAfterRecording`
- **URL**: `https://us-central1-{project-id}.cloudfunctions.net/calculateUsageAfterRecording`
- **方法**: POST (Firebase Callable)
- **用途**: 工作流程 3 - 錄音後計算
- **輸入**:
```typescript
{
  deviceId: string
  sessionStart: string  // ISO 8601 格式
  sessionEnd: string    // ISO 8601 格式
  features: string[]    // ['ai-speech-to-text', 'direct-speech-to-text']
  platform: string     // 'windows' | 'macos' | 'ios' | 'android'
  tokensUsed?: number   // AI 處理使用的 tokens
}
```
- **輸出**:
```typescript
{
  success: boolean
  secondsUsed: number
  tokensUsed?: number
  adjustedTime?: boolean
  message?: string
}
```

### 3. 設備管理 Functions

#### `registerDevice`
- **URL**: `https://us-central1-{project-id}.cloudfunctions.net/registerDevice`
- **方法**: POST (Firebase Callable)
- **用途**: 註冊新設備
- **輸入**:
```typescript
{
  deviceId: string
  deviceName: string
  platform: 'windows' | 'macos' | 'ios' | 'android'
  deviceFingerprint: string
  appVersion: string
  osVersion: string
}
```
- **輸出**:
```typescript
{
  success: boolean
  message?: string
  deviceLimitReached?: boolean
}
```

#### `submitUsage`
- **URL**: `https://us-central1-{project-id}.cloudfunctions.net/submitUsage`
- **方法**: POST (Firebase Callable)
- **用途**: 提交使用量記錄（備用方法）
- **輸入**: 與 `calculateUsageAfterRecording` 相同
- **輸出**: 與 `calculateUsageAfterRecording` 相同

## 🔧 環境變數配置

在您的 Firebase Functions 中需要設定以下環境變數：

```bash
# 在 Firebase Functions 目錄中執行
firebase functions:config:set \
  web.portal_url="https://your-speechpilot-web-portal.com" \
  security.encryption_key="your-encryption-key" \
  security.max_session_hours="24" \
  security.clock_skew_minutes="5"
```

或者使用 `.env` 文件：
```env
WEB_PORTAL_URL=https://your-speechpilot-web-portal.com
SECURITY_ENCRYPTION_KEY=your-encryption-key
MAX_SESSION_HOURS=24
CLOCK_SKEW_MINUTES=5
```

## 📊 訂閱計劃配置

Functions 需要支援以下訂閱計劃：

```typescript
const SUBSCRIPTION_PLANS = {
  FREE: {
    name: 'Free',
    dailyLimitSeconds: 300,      // 5 分鐘
    monthlyLimitSeconds: 300,    // 5 分鐘
    maxDevices: 1,
    features: ['basic-speech-to-text']
  },
  STARTER: {
    name: 'Starter',
    dailyLimitSeconds: 3600,     // 1 小時
    monthlyLimitSeconds: 3600,   // 1 小時
    maxDevices: 1,
    features: ['basic-speech-to-text', 'ai-speech-to-text']
  },
  PRO: {
    name: 'Pro',
    dailyLimitSeconds: 7200,     // 2 小時
    monthlyLimitSeconds: 7200,   // 2 小時
    maxDevices: 2,
    features: ['basic-speech-to-text', 'ai-speech-to-text', 'real-time-streaming']
  },
  PREMIUM: {
    name: 'Premium',
    dailyLimitSeconds: 28800,    // 8 小時
    monthlyLimitSeconds: 28800,  // 8 小時
    maxDevices: 5,
    features: ['basic-speech-to-text', 'ai-speech-to-text', 'real-time-streaming', 'advanced-ai']
  },
  MAX: {
    name: 'Max',
    dailyLimitSeconds: -1,       // 無限制
    monthlyLimitSeconds: -1,     // 無限制
    maxDevices: -1,              // 無限制
    features: ['all']
  }
}
```

## 🔒 安全要求

### 1. 認證檢查
所有 Functions 必須驗證用戶認證：
```typescript
if (!context.auth) {
  throw new HttpsError('unauthenticated', '用戶未認證')
}
```

### 2. 輸入驗證
所有輸入參數必須進行驗證：
```typescript
if (!data.deviceId || typeof data.deviceId !== 'string') {
  throw new HttpsError('invalid-argument', '無效的設備 ID')
}
```

### 3. 時間驗證
檢查客戶端時間與伺服器時間的偏差：
```typescript
const maxSkewMinutes = 5
const clientTime = new Date(data.sessionStart)
const serverTime = new Date()
const skewMinutes = Math.abs(serverTime.getTime() - clientTime.getTime()) / (1000 * 60)

if (skewMinutes > maxSkewMinutes) {
  throw new HttpsError('invalid-argument', '時間偏差過大')
}
```

## 🚀 部署檢查清單

- [ ] Firebase 專案已創建
- [ ] Firebase Functions 已初始化
- [ ] 所有必需的 Functions 已實作
- [ ] 環境變數已設定
- [ ] Firestore 安全規則已配置
- [ ] Functions 已部署到生產環境
- [ ] 客戶端 Firebase 配置已更新
- [ ] 端到端測試已通過

## 📞 客戶端調用範例

```typescript
import { httpsCallable } from 'firebase/functions'
import { functions } from './firebase'

// 檢查訂閱狀態
const checkStatus = httpsCallable(functions, 'checkUserSubscriptionStatus')
const result = await checkStatus()

// 錄音前檢查
const checkUsage = httpsCallable(functions, 'checkUsageBeforeRecording')
const canRecord = await checkUsage({ estimatedDurationSeconds: 60 })

// 錄音後計算
const calculateUsage = httpsCallable(functions, 'calculateUsageAfterRecording')
const usage = await calculateUsage({
  deviceId: 'device-123',
  sessionStart: '2024-01-01T10:00:00Z',
  sessionEnd: '2024-01-01T10:01:00Z',
  features: ['ai-speech-to-text'],
  platform: 'windows'
})
```

## 🔄 測試 URL

部署完成後，您可以使用以下 URL 測試 Functions：

```
https://us-central1-{your-project-id}.cloudfunctions.net/checkUserSubscriptionStatus
https://us-central1-{your-project-id}.cloudfunctions.net/checkUsageBeforeRecording
https://us-central1-{your-project-id}.cloudfunctions.net/calculateUsageAfterRecording
```

請將 `{your-project-id}` 替換為您的實際 Firebase 專案 ID。
