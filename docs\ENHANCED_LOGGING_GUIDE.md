# 🔍 增強日誌記錄指南

**版本**: 1.0  
**更新日期**: 2025-07-25  
**狀態**: ✅ 已實現並可用

## 🎯 概述

為了更好地調試 Firebase Functions 調用問題，我們實現了增強的日誌記錄系統，提供詳細的 Function 調用和回應資訊。

## 🔧 新增功能

### 1. FirebaseFunctionLogger 類

位置：`src/main/utils/FirebaseFunctionLogger.ts`

**功能**:
- 詳細記錄每個 Firebase Function 的調用 URL
- 記錄請求參數和回應數據
- 提供詳細的錯誤資訊，包括錯誤代碼、訊息和詳情
- 計算調用時間
- 支援批量調用和統計

### 2. 詳細日誌格式

#### 成功調用日誌
```
🔗 [Firebase Function] 調用: createOrUpdateUser
📍 [Firebase Function] URL: https://asia-east1-speechpilot-f1495.cloudfunctions.net/createOrUpdateUser
📤 [Firebase Function] 請求參數:
{
  "uid": "NyEr0mIa17X0nxjnHwJfDtJQbre2",
  "email": "<EMAIL>",
  "name": "<PERSON> <PERSON>",
  "authProvider": "google",
  "emailVerified": true,
  "platform": "win32",
  "appVersion": "1.0.0"
}
✅ [Firebase Function] createOrUpdateUser 調用成功 (245ms)
📥 [Firebase Function] 回應:
{
  "success": true,
  "data": {
    "user": {
      "id": "NyEr0mIa17X0nxjnHwJfDtJQbre2",
      "email": "<EMAIL>",
      "isNewUser": true
    }
  }
}
```

#### 錯誤調用日誌
```
🔗 [Firebase Function] 調用: register_device_v2
📍 [Firebase Function] URL: https://asia-east1-speechpilot-f1495.cloudfunctions.net/register_device_v2
📤 [Firebase Function] 請求參數:
{
  "device_id": "desktop-abc123",
  "device_name": "Windows Device",
  "platform": "windows",
  "app_version": "1.0.0"
}
❌ [Firebase Function] register_device_v2 調用失敗 (156ms)
📍 [Firebase Function] URL: https://asia-east1-speechpilot-f1495.cloudfunctions.net/register_device_v2
🔴 [Firebase Function] 錯誤代碼: functions/failed-precondition
🔴 [Firebase Function] 錯誤訊息: 用戶訂閱不存在，請先完成用戶註冊
🔴 [Firebase Function] 完整錯誤對象:
{
  "name": "FirebaseError",
  "code": "functions/failed-precondition",
  "message": "用戶訂閱不存在，請先完成用戶註冊",
  "details": undefined,
  "customData": undefined
}
```

## 📋 已更新的服務

### 1. AuthService
- `createOrUpdateUser()` - 創建/更新用戶記錄
- `checkSubscriptionStatus()` - 檢查訂閱狀態
- `getUserInfo()` - 獲取用戶資訊

### 2. UsageTrackingService
- `registerDevice()` - 設備註冊
- `validateDevice()` - 設備驗證
- `checkUsageBeforeRecording()` - 錄音前檢查
- `submitUsage()` - 提交使用量

### 3. Firebase.ts
- `verify_token` 調用（在 Google 登入後）

## 🔍 調試優勢

### 1. 清晰的 Function 識別
- 每個調用都顯示完整的 Function URL
- 可以直接看到調用的是哪個 Function

### 2. 詳細的請求/回應資訊
- 完整的請求參數 JSON
- 完整的回應數據 JSON
- 調用時間統計

### 3. 增強的錯誤資訊
- Firebase 錯誤代碼
- 詳細錯誤訊息
- 錯誤詳情和自定義數據
- 完整的錯誤對象（用於深度調試）

## 🚀 使用方法

### 基本調用
```typescript
import { FirebaseFunctionLogger } from '../utils/FirebaseFunctionLogger'

// 替代原來的 httpsCallable 調用
const result = await FirebaseFunctionLogger.call('functionName', data)
```

### 批量調用
```typescript
const results = await FirebaseFunctionLogger.callBatch([
  { name: 'user', functionName: 'createOrUpdateUser', data: userData },
  { name: 'device', functionName: 'register_device_v2', data: deviceData }
])
```

### 健康檢查
```typescript
const isHealthy = await FirebaseFunctionLogger.healthCheck()
```

### 調用統計
```typescript
// 獲取統計數據
const stats = FirebaseFunctionLogger.getCallStats()

// 記錄統計到控制台
FirebaseFunctionLogger.logCallStats()
```

## 🔧 配置選項

### 超時設定
```typescript
const result = await FirebaseFunctionLogger.call('functionName', data, {
  timeout: 30000 // 30 秒超時
})
```

## 📊 預期的日誌改進

### 之前的日誌
```
設備註冊失敗: [FunctionsError [FirebaseError]: not-found]
```

### 現在的日誌
```
🔗 [Firebase Function] 調用: register_device_v2
📍 [Firebase Function] URL: https://asia-east1-speechpilot-f1495.cloudfunctions.net/register_device_v2
📤 [Firebase Function] 請求參數: { "device_id": "...", "platform": "windows" }
❌ [Firebase Function] register_device_v2 調用失敗 (156ms)
🔴 [Firebase Function] 錯誤代碼: functions/not-found
🔴 [Firebase Function] 錯誤訊息: Function not found
```

## 🎯 調試工作流程

1. **查看 Function URL** - 確認調用的是正確的 Function
2. **檢查請求參數** - 驗證發送的數據格式是否正確
3. **分析錯誤代碼** - 根據 Firebase 錯誤代碼判斷問題類型
4. **查看錯誤詳情** - 獲取更多錯誤上下文資訊
5. **檢查調用時間** - 識別性能問題

## 🚨 常見錯誤代碼解釋

| 錯誤代碼 | 含義 | 可能原因 |
|---------|------|----------|
| `functions/not-found` | Function 不存在 | Function 未部署或名稱錯誤 |
| `functions/unauthenticated` | 用戶未認證 | Firebase Auth token 無效 |
| `functions/permission-denied` | 權限不足 | 用戶無權限或資源限制 |
| `functions/failed-precondition` | 前置條件失敗 | 業務邏輯檢查失敗 |
| `functions/resource-exhausted` | 資源耗盡 | 達到使用限制 |
| `functions/internal` | 內部錯誤 | Function 執行錯誤 |

## 📈 效益

1. **更快的問題定位** - 立即知道哪個 Function 調用失敗
2. **更好的錯誤理解** - 詳細的錯誤資訊幫助快速解決問題
3. **更容易的調試** - 完整的請求/回應日誌
4. **性能監控** - 調用時間統計幫助識別性能問題
5. **更好的開發體驗** - 清晰的日誌格式提高開發效率

現在當你運行應用並嘗試登入時，你將看到詳細的 Firebase Function 調用日誌，包括 URL、參數、回應和錯誤資訊！
