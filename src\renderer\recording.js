const { ipc<PERSON><PERSON><PERSON> } = require('electron')

let startTime = Date.now()
let durationInterval = null

// 狀態圖標映射
const statusIcons = {
    recording: '🎤',
    processing: '⚙️',
    completed: '✅',
    error: '❌'
}

// 模式文本映射
const modeTexts = {
    ai: 'AI 智能模式',
    direct: '直接轉錄模式'
}

// 狀態文本映射
const statusTexts = {
    recording: '正在錄音...',
    processing: '處理中...',
    completed: '完成',
    error: '錯誤'
}

// 監聽狀態更新
ipcRenderer.on('status-update', (event, status) => {
    updateUI(status)
})

function updateUI(status) {
    const icon = document.getElementById('recordingIcon')
    const modeText = document.getElementById('modeText')
    const statusText = document.getElementById('statusText')
    const message = document.getElementById('message')

    // 更新圖標
    icon.textContent = statusIcons[status.status] || '🎤'
    icon.className = `recording-icon ${status.status}`

    // 更新模式文本
    modeText.textContent = modeTexts[status.mode] || 'AI 智能模式'

    // 更新狀態文本
    statusText.textContent = statusTexts[status.status] || '正在錄音...'

    // 更新消息
    if (status.message) {
        message.textContent = status.message
        message.style.display = 'block'
    } else {
        message.style.display = 'none'
    }

    // 更新音量檢測
    if (typeof status.volume === 'number') {
        const volumeFill = document.getElementById('volumeFill')
        const volumeText = document.getElementById('volumeText')
        const volume = Math.max(0, Math.min(100, status.volume))
        
        volumeFill.style.width = volume + '%'
        volumeText.textContent = volume + '%'
    }

    // 管理計時器 - 只在真正錄音時運行計時器
    if (status.isRecording && status.status === 'recording') {
        startDurationTimer()
    } else {
        stopDurationTimer()
    }
}

function startDurationTimer() {
    if (durationInterval) return

    startTime = Date.now()
    durationInterval = setInterval(() => {
        const elapsed = Math.floor((Date.now() - startTime) / 1000)
        const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0')
        const seconds = (elapsed % 60).toString().padStart(2, '0')
        document.getElementById('duration').textContent = `${minutes}:${seconds}`
    }, 1000)
}

function stopDurationTimer() {
    if (durationInterval) {
        clearInterval(durationInterval)
        durationInterval = null
    }
}

// 全局停止狀態管理
let isStoppingInProgress = false

// 更新停止按鈕狀態的統一函數
function updateStopButtonState(state) {
    const stopBtn = document.getElementById('stopBtn')
    if (!stopBtn) return

    const stopIcon = stopBtn.querySelector('.stop-icon')
    const stopText = stopBtn.querySelector('.stop-text')

    // 清除所有狀態類
    stopBtn.classList.remove('stopping', 'stopped')

    switch(state) {
        case 'stopping':
            stopBtn.classList.add('stopping')
            stopIcon.textContent = '⏳'
            stopText.textContent = '停止中...'
            break
        case 'stopped':
            stopBtn.classList.add('stopped')
            stopIcon.textContent = '✅'
            stopText.textContent = '已停止'
            break
        default: // 'normal'
            stopIcon.textContent = '⏹'
            stopText.textContent = '停止錄音'
    }
}

// 停止錄音函數 (替代原來的關閉窗口函數)
function stopRecording() {
    // 防止重複執行
    if (isStoppingInProgress) {
        return
    }

    isStoppingInProgress = true

    // 設置為停止中狀態
    updateStopButtonState('stopping')

    // 模擬停止過程
    setTimeout(() => {
        updateStopButtonState('stopped')

        // 實際停止錄音
        if (typeof require !== 'undefined') {
            const { ipcRenderer } = require('electron')
            ipcRenderer.send('close-recording-window')
        } else {
            // 瀏覽器環境下的模擬
            console.log('模擬停止錄音')
            setTimeout(() => window.close(), 1000)
        }
    }, 800) // 800ms 後完成停止
}

// 暴露給全局的函數，供主進程調用
window.triggerStopRecording = stopRecording
window.updateStopButtonState = updateStopButtonState

// 保留舊的函數名以保持兼容性
function closeWindow() {
    stopRecording()
}

// 窗口拖拽功能 - 使用 CSS 拖動區域
document.addEventListener('DOMContentLoaded', () => {
    const dragArea = document.getElementById('dragArea')
    if (dragArea) {
        // 使用 CSS 的 -webkit-app-region: drag 來處理拖動
        // 這樣可以避免 JavaScript 拖動導致的閃爍問題
        console.log('✅ Drag area initialized with CSS drag region')
    }
})
