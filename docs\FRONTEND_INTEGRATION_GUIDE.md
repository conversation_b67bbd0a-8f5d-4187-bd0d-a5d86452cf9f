# 🔧 前端整合指南 - 解決 SSO 問題

**完整的解決方案：Firebase Auth + 新增 API 函數**

## ✅ 問題已解決

### 🚨 原問題
- Google 登入成功 ✅
- 設備註冊失敗 ❌ - functions/not-found 錯誤
- Token 驗證失敗 ❌ - functions/internal 錯誤
- 創建用戶記錄失敗 ❌ - functions/not-found 錯誤
- 檢查訂閱狀態失敗 ❌ - functions/not-found 錯誤

### 🎯 解決方案
✅ **新增缺少的函數**：
- `create_or_update_user` - 創建/更新用戶
- `register_device_v2` - 統一設備註冊接口
- `validate_device` - 增強設備驗證
- `check_user_subscription_status` - 完整訂閱狀態檢查

✅ **統一資料結構**：
- 移除重複的 `purchases` 集合
- 統一使用 `subscriptions` 集合
- 標準化 `devices` 和 `usage` 集合

## 🚀 立即可用的 API

### 基本配置
```javascript
import { getFunctions, httpsCallable } from 'firebase/functions';

// 重要：必須指定 asia-east1 區域
const functions = getFunctions(app, 'asia-east1');
```

### 1. 用戶註冊/更新
```javascript
const createOrUpdateUser = httpsCallable(functions, 'create_or_update_user');

const handleUserRegistration = async (user) => {
  try {
    const result = await createOrUpdateUser({
      uid: user.uid,
      email: user.email,
      name: user.displayName,
      image: user.photoURL,
      auth_provider: 'google',
      email_verified: user.emailVerified,
      platform: 'windows', // 或 'macos'
      app_version: '1.0.0'
    });
    
    console.log('用戶註冊成功:', result.data);
    return result.data;
  } catch (error) {
    console.error('用戶註冊失敗:', error);
    throw error;
  }
};
```

### 2. 設備註冊
```javascript
const registerDevice = httpsCallable(functions, 'register_device_v2');

const handleDeviceRegistration = async () => {
  try {
    const result = await registerDevice({
      device_id: getDeviceId(), // 你的設備ID生成邏輯
      device_name: getDeviceName(),
      platform: getPlatform(), // 'windows' | 'macos' | 'ios' | 'android'
      app_version: '1.0.0',
      device_info: {
        os_version: getOSVersion(),
        fingerprint: getDeviceFingerprint()
      }
    });
    
    console.log('設備註冊成功:', result.data);
    return result.data;
  } catch (error) {
    console.error('設備註冊失敗:', error);
    throw error;
  }
};
```

### 3. 設備驗證
```javascript
const validateDevice = httpsCallable(functions, 'validate_device');

const handleDeviceValidation = async (deviceId, platform) => {
  try {
    const result = await validateDevice({
      device_id: deviceId,
      platform: platform
    });
    
    if (result.data.success && result.data.data.has_access) {
      console.log('設備驗證成功');
      return true;
    } else {
      console.log('設備存取被拒絕:', result.data.reason);
      return false;
    }
  } catch (error) {
    console.error('設備驗證失敗:', error);
    return false;
  }
};
```

### 4. 檢查訂閱狀態
```javascript
const checkSubscription = httpsCallable(functions, 'check_user_subscription_status');

const handleSubscriptionCheck = async () => {
  try {
    const result = await checkSubscription();
    
    const subscriptionData = result.data.data;
    console.log('訂閱狀態:', subscriptionData);
    
    return {
      plan: subscriptionData.subscription.plan,
      canUse: subscriptionData.limits.can_use,
      dailyRemaining: subscriptionData.usage.daily_remaining,
      deviceCount: subscriptionData.devices.current_count,
      maxDevices: subscriptionData.devices.max_devices
    };
  } catch (error) {
    console.error('訂閱狀態檢查失敗:', error);
    throw error;
  }
};
```

## 🔄 完整的登入流程

```javascript
import { authService } from './auth-service.js'; // 使用之前提供的 Firebase Auth 代碼

class SpeechPilotApp {
  constructor() {
    this.user = null;
    this.setupAuthListener();
  }

  setupAuthListener() {
    authService.onAuthStateChanged(async (user) => {
      if (user) {
        console.log('用戶已登入:', user.email);
        this.user = user;
        await this.initializeUserSession();
      } else {
        console.log('用戶未登入');
        this.user = null;
        this.showLoginScreen();
      }
    });
  }

  async handleGoogleSignIn() {
    try {
      // 1. Google 登入
      const authResult = await authService.signInWithGoogle();
      
      if (authResult.success) {
        console.log('✅ Google 登入成功');
        
        // 2. 創建/更新用戶記錄
        await this.createOrUpdateUser(authResult.user);
        
        // 3. 註冊設備
        await this.registerDevice();
        
        // 4. 驗證設備存取
        await this.validateDeviceAccess();
        
        // 5. 檢查訂閱狀態
        await this.checkSubscriptionStatus();
        
        console.log('🎉 完整登入流程成功！');
        
      } else {
        console.error('❌ Google 登入失敗:', authResult.error);
      }
    } catch (error) {
      console.error('❌ 登入流程錯誤:', error);
      this.handleLoginError(error);
    }
  }

  async createOrUpdateUser(user) {
    const createOrUpdateUser = httpsCallable(functions, 'create_or_update_user');
    
    const result = await createOrUpdateUser({
      uid: user.uid,
      email: user.email,
      name: user.displayName,
      image: user.photoURL,
      auth_provider: 'google',
      email_verified: user.emailVerified,
      platform: this.getPlatform(),
      app_version: '1.0.0'
    });
    
    console.log('✅ 用戶記錄創建/更新成功');
    return result.data;
  }

  async registerDevice() {
    const registerDevice = httpsCallable(functions, 'register_device_v2');
    
    const result = await registerDevice({
      device_id: this.getDeviceId(),
      device_name: this.getDeviceName(),
      platform: this.getPlatform(),
      app_version: '1.0.0',
      device_info: this.getDeviceInfo()
    });
    
    console.log('✅ 設備註冊成功');
    return result.data;
  }

  async validateDeviceAccess() {
    const validateDevice = httpsCallable(functions, 'validate_device');
    
    const result = await validateDevice({
      device_id: this.getDeviceId(),
      platform: this.getPlatform()
    });
    
    if (result.data.success && result.data.data.has_access) {
      console.log('✅ 設備存取驗證成功');
      return true;
    } else {
      throw new Error(`設備存取被拒絕: ${result.data.reason}`);
    }
  }

  async checkSubscriptionStatus() {
    const checkSubscription = httpsCallable(functions, 'check_user_subscription_status');
    
    const result = await checkSubscription();
    const data = result.data.data;
    
    console.log('✅ 訂閱狀態檢查成功');
    console.log(`計劃: ${data.subscription.plan}`);
    console.log(`今日剩餘: ${data.usage.daily_remaining} 秒`);
    console.log(`設備數量: ${data.devices.current_count}/${data.devices.max_devices}`);
    
    return data;
  }

  handleLoginError(error) {
    // 根據錯誤類型顯示適當的錯誤訊息
    if (error.code === 'functions/unauthenticated') {
      alert('認證失敗，請重新登入');
    } else if (error.code === 'functions/permission-denied') {
      alert('權限不足，請檢查您的帳戶狀態');
    } else if (error.code === 'functions/resource-exhausted') {
      alert('已達到設備數量限制，請升級您的計劃');
    } else {
      alert(`登入失敗: ${error.message}`);
    }
  }

  // 工具函數（與之前相同）
  getDeviceId() {
    let deviceId = localStorage.getItem('device_id');
    if (!deviceId) {
      deviceId = 'desktop-' + Math.random().toString(36).substr(2, 9);
      localStorage.setItem('device_id', deviceId);
    }
    return deviceId;
  }

  getDeviceName() {
    return navigator.platform || 'Desktop Computer';
  }

  getPlatform() {
    if (navigator.platform.includes('Win')) return 'windows';
    if (navigator.platform.includes('Mac')) return 'macos';
    return 'windows'; // 預設
  }

  getDeviceInfo() {
    return {
      os_version: navigator.platform,
      user_agent: navigator.userAgent,
      screen_resolution: `${screen.width}x${screen.height}`,
      language: navigator.language,
      fingerprint: this.generateDeviceFingerprint()
    };
  }

  generateDeviceFingerprint() {
    // 簡單的設備指紋生成
    const data = [
      navigator.userAgent,
      navigator.language,
      screen.width,
      screen.height,
      new Date().getTimezoneOffset()
    ].join('|');
    
    return btoa(data).substr(0, 16);
  }
}

// 初始化應用
const app = new SpeechPilotApp();

// 綁定登入按鈕
document.getElementById('google-signin-btn').addEventListener('click', () => {
  app.handleGoogleSignIn();
});
```

## 🎯 測試步驟

1. **設置 Firebase 配置**（如果還沒有）
2. **複製上述代碼**到你的應用中
3. **測試登入流程**：
   - 點擊 Google 登入
   - 檢查控制台輸出
   - 確認所有步驟都成功

## 📞 如果遇到問題

### 常見錯誤和解決方案：

1. **functions/not-found**
   - 確認函數名稱正確
   - 確認指定了 `asia-east1` 區域

2. **functions/unauthenticated**
   - 確認用戶已登入
   - 檢查 Firebase Auth 配置

3. **functions/permission-denied**
   - 檢查平台支援（FREE 計劃只支援 Windows/macOS）
   - 檢查設備數量限制

4. **functions/resource-exhausted**
   - 已達到設備數量限制
   - 需要升級計劃

## 🎉 成功指標

當你看到以下輸出時，表示整合成功：
```
✅ Google 登入成功
✅ 用戶記錄創建/更新成功
✅ 設備註冊成功
✅ 設備存取驗證成功
✅ 訂閱狀態檢查成功
🎉 完整登入流程成功！
```

**現在前端團隊可以開始完整的整合了！** 🚀
