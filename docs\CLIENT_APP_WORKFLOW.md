# 📱 SpeechPilot 客戶端應用工作流程

**版本**: 1.0  
**更新日期**: 2025-07-25  
**狀態**: ✅ 已部署並可用

## 🎯 概述

本文檔描述 SpeechPilot 客戶端應用的完整工作流程，包括用戶認證、設備管理、訂閱檢查和使用量追蹤的詳細流程。

## 🔄 核心工作流程

### 1. 應用啟動流程

```mermaid
graph TD
    A[應用啟動] --> B[初始化 Firebase]
    B --> C[檢查認證狀態]
    C --> D{用戶已登入?}
    D -->|是| E[載入用戶資料]
    D -->|否| F[顯示登入畫面]
    E --> G[註冊/驗證設備]
    G --> H[檢查訂閱狀態]
    H --> I[進入主應用]
    F --> J[用戶選擇登入方式]
    J --> K[執行 OAuth 流程]
    K --> L[新用戶檢測]
```

### 2. 新用戶 vs 現有用戶檢測規則

#### 🆕 新用戶檢測條件
- **Firestore `users` 集合中不存在該 UID**
- **或者 `subscriptions` 集合中不存在該用戶記錄**

#### 📋 新用戶處理流程
```typescript
// 檢測規則
if (!userExists || !subscriptionExists) {
  // 新用戶流程
  await createOrUpdateUser(userData)  // 創建用戶記錄 + Free 計劃
  await registerDevice(deviceInfo)    // 註冊設備
  await validateDevice(deviceId)      // 驗證設備存取
} else {
  // 現有用戶流程
  await createOrUpdateUser(userData)  // 更新用戶資料
  await registerDevice(deviceInfo)    // 註冊/更新設備
  await validateDevice(deviceId)      // 驗證設備存取
}
```

## 🔐 認證工作流程

### Google OAuth 流程

```typescript
// 1. 用戶點擊 Google 登入
async function handleGoogleSignIn() {
  try {
    // 開啟外部瀏覽器進行 OAuth
    const user = await signInWithGoogle()
    
    // 2. 檢查是否為新用戶
    const isNewUser = await checkIfNewUser(user.uid)
    
    // 3. 創建或更新用戶記錄
    const userResult = await createOrUpdateUser({
      uid: user.uid,
      email: user.email,
      name: user.displayName,
      image: user.photoURL,
      authProvider: 'google',
      emailVerified: user.emailVerified,
      platform: process.platform,
      appVersion: app.getVersion()
    })
    
    if (isNewUser) {
      console.log('🆕 新用戶註冊完成，已自動分配 Free 計劃')
    } else {
      console.log('👤 現有用戶資料已更新')
    }
    
    // 4. 繼續設備註冊流程
    await handleDeviceRegistration()
    
  } catch (error) {
    handleAuthError(error)
  }
}
```

### 外部瀏覽器關閉修復

✅ **已修復問題**: 瀏覽器倒數計時結束後不會自動關閉

**修復內容**:
- 增強了 `window.close()` 的多重嘗試機制
- 添加了手動關閉按鈕作為備用方案
- 改善了跨瀏覽器兼容性

## 📱 設備管理工作流程

### 設備註冊流程

```typescript
async function handleDeviceRegistration() {
  try {
    // 1. 生成設備資訊
    const deviceInfo = {
      device_id: getDeviceId(),           // 持久化設備 ID
      device_name: getDeviceName(),       // 設備名稱
      platform: getPlatform(),           // windows/macos
      app_version: app.getVersion(),      // 應用版本
      device_info: {
        os_version: process.getSystemVersion(),
        fingerprint: generateDeviceFingerprint()
      }
    }
    
    // 2. 調用設備註冊 API
    const result = await register_device_v2(deviceInfo)
    
    if (result.success) {
      console.log(`✅ 設備註冊成功: ${result.data.device_count}/${result.data.max_devices}`)
      
      // 3. 驗證設備存取權限
      await handleDeviceValidation(deviceInfo.device_id, deviceInfo.platform)
    } else {
      throw new Error('設備註冊失敗')
    }
    
  } catch (error) {
    handleDeviceError(error)
  }
}
```

### 設備驗證流程

```typescript
async function handleDeviceValidation(deviceId, platform) {
  try {
    const result = await validate_device({
      device_id: deviceId,
      platform: platform
    })
    
    if (result.data.has_access) {
      console.log('✅ 設備驗證成功，可以使用應用')
      await checkSubscriptionStatus()
    } else {
      handleAccessDenied(result.data.reason)
    }
    
  } catch (error) {
    console.error('❌ 設備驗證失敗:', error)
    throw error
  }
}
```

## 💳 訂閱狀態檢查工作流程

### 訂閱檢查流程

```typescript
async function checkSubscriptionStatus() {
  try {
    const result = await check_user_subscription_status()
    const data = result.data
    
    // 顯示訂閱資訊
    console.log(`📋 當前計劃: ${data.planName}`)
    console.log(`⏰ 今日剩餘: ${data.availableSecondsToday} 秒`)
    console.log(`📱 設備數量: ${data.deviceCount}/${data.maxDevices}`)
    
    if (data.canUse) {
      console.log('✅ 可以開始使用應用')
      initializeMainApp(data)
    } else {
      if (data.needsUpgrade) {
        showUpgradePrompt(data.upgradeUrl)
      } else {
        showSubscriptionError(data)
      }
    }
    
  } catch (error) {
    console.error('❌ 訂閱狀態檢查失敗:', error)
    // 允許用戶繼續使用，但顯示警告
    initializeMainApp(null)
  }
}
```

## 🎙️ 錄音使用流程

### 錄音前檢查

```typescript
async function startRecording() {
  try {
    // 1. 錄音前檢查使用量
    const checkResult = await check_usage_before_recording({
      device_id: getDeviceId(),
      features: ['ai-speech-to-text']
    })
    
    if (!checkResult.data.can_use) {
      showUsageLimitError(checkResult.data)
      return false
    }
    
    console.log(`✅ 可以開始錄音，剩餘 ${checkResult.data.remaining_seconds} 秒`)
    
    // 2. 開始錄音
    const sessionStart = new Date()
    await startAudioRecording()
    
    // 3. 錄音結束後提交使用量
    const sessionEnd = new Date()
    const duration = Math.floor((sessionEnd - sessionStart) / 1000)
    
    await submitUsage(duration)
    
  } catch (error) {
    console.error('❌ 錄音流程錯誤:', error)
    handleRecordingError(error)
  }
}
```

### 使用量提交

```typescript
async function submitUsage(durationSeconds) {
  try {
    const result = await submit_usage({
      duration_seconds: durationSeconds,
      feature_type: 'ai-speech-to-text',
      device_id: getDeviceId()
    })
    
    console.log(`📊 使用量已記錄: ${durationSeconds} 秒`)
    console.log(`⏰ 今日剩餘: ${result.data.remaining_quota} 秒`)
    
    if (result.data.limit_exceeded) {
      showDailyLimitReached()
    }
    
  } catch (error) {
    console.error('❌ 使用量提交失敗:', error)
    // 不阻止用戶繼續使用，但記錄錯誤
  }
}
```

## 🚨 錯誤處理工作流程

### 認證錯誤處理

```typescript
function handleAuthError(error) {
  switch (error.code) {
    case 'functions/unauthenticated':
      showError('認證失敗，請重新登入')
      redirectToLogin()
      break
      
    case 'functions/permission-denied':
      showError('權限不足，請檢查您的帳戶狀態')
      break
      
    case 'functions/not-found':
      showError('服務暫時不可用，請稍後再試')
      break
      
    default:
      showError(`登入失敗: ${error.message}`)
  }
}
```

### 設備錯誤處理

```typescript
function handleDeviceError(error) {
  switch (error.code) {
    case 'functions/resource-exhausted':
      showDeviceLimitError(error.details)
      break
      
    case 'functions/permission-denied':
      if (error.message.includes('平台')) {
        showPlatformNotSupportedError()
      } else {
        showDeviceOwnershipError()
      }
      break
      
    default:
      showError(`設備註冊失敗: ${error.message}`)
  }
}
```

## 📊 Firebase Functions 對應表

| 客戶端操作 | Firebase Function | 用途 |
|-----------|------------------|------|
| 用戶登入後 | `createOrUpdateUser` | 創建/更新用戶記錄，新用戶自動分配 Free 計劃 |
| 設備註冊 | `register_device_v2` | 註冊新設備或更新現有設備資訊 |
| 設備驗證 | `validate_device` | 驗證設備存取權限和所有權 |
| 訂閱檢查 | `check_user_subscription_status` | 獲取完整的訂閱和使用量資訊 |
| 錄音前檢查 | `check_usage_before_recording` | 檢查是否可以開始錄音 |
| 使用量提交 | `submit_usage` | 記錄實際使用時間 |
| 會話結束 | `end_session` | 清理會話狀態 |
| Token 驗證 | `verify_token` | 驗證用戶認證狀態 |

## 🔧 開發團隊注意事項

### 1. 新用戶檢測邏輯
- **不要**依賴客戶端判斷是否為新用戶
- **使用** `createOrUpdateUser` Function 的回應中的 `isNewUser` 欄位
- **確保** 所有新用戶都會自動獲得 Free 計劃

### 2. 錯誤處理策略
- **認證錯誤**: 必須處理，引導用戶重新登入
- **設備錯誤**: 可以降級處理，但要通知用戶
- **使用量錯誤**: 不阻止功能使用，但要記錄

### 3. 離線處理
- **認證狀態**: 本地緩存，離線時使用緩存
- **使用量**: 離線時本地記錄，上線後同步
- **設備驗證**: 定期重新驗證，不要過於頻繁

### 4. 性能優化
- **批量操作**: 盡量合併 API 調用
- **緩存策略**: 訂閱狀態可以緩存 5 分鐘
- **錯誤重試**: 網路錯誤自動重試 3 次

## 🔧 客戶端實現範例

### 主應用初始化

```typescript
import { ServiceManager } from './services/ServiceManager'

class SpeechPilotApp {
  private serviceManager: ServiceManager

  constructor() {
    this.serviceManager = new ServiceManager()
    this.setupEventListeners()
  }

  async initialize() {
    await this.serviceManager.initialize()

    // 檢查認證狀態
    if (this.serviceManager.isAuthenticated()) {
      console.log('✅ 用戶已登入，初始化會話...')
      await this.initializeUserSession()
    } else {
      console.log('❌ 用戶未登入，顯示登入畫面')
      this.showLoginWindow()
    }
  }

  private setupEventListeners() {
    this.serviceManager.on('auth-state-changed', (isAuthenticated) => {
      if (isAuthenticated) {
        this.hideLoginWindow()
        this.initializeUserSession()
      } else {
        this.showLoginWindow()
      }
    })

    this.serviceManager.on('device-registration-failed', (error) => {
      this.showError(`設備註冊失敗: ${error}`)
    })

    this.serviceManager.on('usage-session-started', () => {
      this.updateRecordingUI(true)
    })

    this.serviceManager.on('usage-session-ended', () => {
      this.updateRecordingUI(false)
    })
  }

  // 開始錄音
  async startRecording() {
    const result = await this.serviceManager.startRecordingWorkflow()

    if (result.canStart) {
      console.log(`✅ ${result.message}`)
      console.log(`⏰ 剩餘時間: ${result.availableSeconds} 秒`)
      // 開始實際錄音邏輯
      this.startAudioRecording()
    } else {
      console.error(`❌ ${result.message}`)
      this.showError(result.message)
    }
  }

  // 結束錄音
  async stopRecording() {
    // 停止實際錄音邏輯
    this.stopAudioRecording()

    const result = await this.serviceManager.endRecordingWorkflow()

    if (result.success) {
      console.log(`✅ ${result.message}`)
      console.log(`⏰ 剩餘時間: ${result.remainingSeconds} 秒`)
    } else {
      console.error(`❌ ${result.message}`)
    }
  }
}
```

### 登入處理

```typescript
// 在登入視窗中
async function handleGoogleLogin() {
  try {
    const success = await serviceManager.signInWithGoogle()

    if (success) {
      console.log('🎉 登入成功！')
      // ServiceManager 會自動處理後續流程：
      // 1. 創建/更新用戶記錄
      // 2. 註冊設備
      // 3. 檢查訂閱狀態
    } else {
      showError('登入失敗，請稍後再試')
    }
  } catch (error) {
    showError(`登入錯誤: ${error.message}`)
  }
}
```

## 🚨 外部瀏覽器關閉問題修復

✅ **已修復**: 外部瀏覽器倒數計時結束後不會自動關閉的問題

**修復內容**:
- 增強了 `window.close()` 的多重嘗試機制
- 添加了手動關閉按鈕作為備用方案
- 改善了跨瀏覽器兼容性
- 添加了自動重試邏輯

**修復位置**: `firebase.ts` 第 118-161 行

## 🚀 部署狀態

✅ **Firebase Functions**: 已部署到 `asia-east1` 區域
✅ **客戶端整合**: 已完成並測試
✅ **錯誤處理**: 已實現完整的錯誤處理機制
✅ **新用戶流程**: 已實現自動 Free 計劃分配
✅ **外部瀏覽器修復**: 已修復自動關閉問題

**API 基礎 URL**: `https://asia-east1-speechpilot-f1495.cloudfunctions.net`

## 📋 開發團隊檢查清單

### 前端開發
- [ ] 更新登入流程以使用新的 `ServiceManager.signInWithGoogle()`
- [ ] 實現錄音前檢查：`ServiceManager.startRecordingWorkflow()`
- [ ] 實現錄音後處理：`ServiceManager.endRecordingWorkflow()`
- [ ] 添加錯誤處理和用戶提示
- [ ] 測試新用戶和現有用戶的不同流程

### 後端開發
- [x] 部署所有必要的 Firebase Functions
- [x] 實現新用戶自動 Free 計劃分配
- [x] 實現設備註冊和驗證邏輯
- [x] 實現使用量追蹤和限制檢查

### 測試
- [ ] 測試新用戶註冊流程
- [ ] 測試現有用戶登入流程
- [ ] 測試設備數量限制
- [ ] 測試使用量限制
- [ ] 測試外部瀏覽器關閉功能
- [ ] 測試錯誤處理機制
