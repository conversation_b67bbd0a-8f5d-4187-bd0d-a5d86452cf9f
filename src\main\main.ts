// SpeechPilot - 重構版本
import { app, globalShortcut, Tray, Menu, nativeImage, ipcMain } from 'electron'
import { unlinkSync, existsSync } from 'fs'
import { join } from 'path'
import { config } from 'dotenv'
import { platform } from 'os'

// 服務導入
import { AudioService } from './services/AudioService'
import { AIService } from './services/AIService'
import { TextInputService } from './services/TextInputService'
import { RecordingWindow } from './windows/RecordingWindow'
import { SettingsWindow } from './windows/SettingsWindow'
import { LoginWindow } from './windows/LoginWindow'
import { getServiceManager } from './services/ServiceManager'

// 載入環境變數
const envPath = join(process.cwd(), '.env.local')
config({ path: envPath })

// 禁用 GPU 加速
app.disableHardwareAcceleration()

class SpeechPilot {
  private isRecording = false
  private tray: Tray | null = null
  private recordingProcess: any = null
  private tempAudioFile = join(__dirname, 'temp_audio.wav')
  
  // 服務實例
  private audioService = new AudioService()
  private textInputService = new TextInputService()
  private aiService = new AIService(this.textInputService)
  private recordingWindow = new RecordingWindow()
  private settingsWindow = new SettingsWindow()
  private loginWindow = new LoginWindow()
  private serviceManager = getServiceManager()

  async init() {
    console.log('🚀 Starting SpeechPilot - Refactored Version...')
    console.log(`💻 Platform: ${platform()}`)

    // 設置 deep link 處理
    this.setupDeepLinkHandling()

    // 檢查環境變數
    if (!process.env.AZURE_OPENAI_API_KEY || !process.env.AZURE_OPENAI_ENDPOINT) {
      console.error('❌ Missing Azure OpenAI configuration')
      return
    }

    console.log('✅ Azure OpenAI configured:', process.env.AZURE_OPENAI_ENDPOINT)
    console.log('✅ Model:', process.env.AZURE_OPENAI_MODEL)
    
    // 設置系統托盤
    this.setupTray()
    
    // 註冊快捷鍵
    this.setupHotkeys()

    // 設置 AI 服務語言
    this.updateAIServiceLanguages()

    // 設置 IPC 處理器
    this.setupIPC()

    // 初始化服務管理器
    await this.serviceManager.initialize()

    // 設置服務管理器事件監聽
    this.setupServiceManagerEvents()

    console.log('✅ SpeechPilot ready!')
  }

  private setupTray() {
    const icon = nativeImage.createEmpty()
    this.tray = new Tray(icon)
    
    const contextMenu = Menu.buildFromTemplate([
      { label: '⚙️ 設置', click: () => this.settingsWindow.show() },
      { type: 'separator' },
      { label: '❌ 退出', click: () => app.quit() }
    ])
    
    this.tray.setContextMenu(contextMenu)
    this.tray.setToolTip('SpeechPilot - AI 語音助手')
  }

  private setupHotkeys() {
    globalShortcut.unregisterAll()

    // 獲取設定中的快捷鍵
    const settings = this.settingsWindow.getSettings()
    const aiHotkey = settings.hotkeys?.ai || 'CommandOrControl+Shift+C'
    const directHotkey = settings.hotkeys?.direct || 'CommandOrControl+Shift+V'

    const aiRegistered = globalShortcut.register(aiHotkey, () => {
      console.log('🎯 AI hotkey triggered!')
      this.toggleRecording('ai')
    })

    const directRegistered = globalShortcut.register(directHotkey, () => {
      console.log('🎯 Direct hotkey triggered!')
      this.toggleRecording('direct')
    })

    console.log(`🎹 Hotkeys: AI=${aiHotkey}=${aiRegistered ? '✅' : '❌'}, Direct=${directHotkey}=${directRegistered ? '✅' : '❌'}`)
  }

  private updateAIServiceLanguages() {
    const settings = this.settingsWindow.getSettings()
    const languages = settings.languages || ['Chinese', 'English']
    this.aiService.setLanguages(languages)
  }

  private setupServiceManagerEvents() {
    // 監聽使用量限制超出事件
    this.serviceManager.on('usage-limit-exceeded', (remainingSeconds: number) => {
      this.showUpgradeDialog(remainingSeconds)
    })

    // 監聽認證狀態變化
    this.serviceManager.on('auth-state-changed', (isAuthenticated: boolean) => {
      if (!isAuthenticated) {
        console.log('用戶已登出，停止當前錄音')
        if (this.isRecording) {
          this.stopRecording()
        }
      }
    })
  }

  private setupDeepLinkHandling() {
    // 註冊自定義 URL scheme
    console.log('🔗 Setting up deep link handling...')

    try {
      if (process.defaultApp) {
        if (process.argv.length >= 2) {
          const result = app.setAsDefaultProtocolClient('speechpilot', process.execPath, [process.argv[1]])
          console.log('🔗 Protocol client set (dev mode):', result)
        }
      } else {
        const result = app.setAsDefaultProtocolClient('speechpilot')
        console.log('🔗 Protocol client set (production):', result)
      }

      // 檢查是否已註冊
      const isRegistered = app.isDefaultProtocolClient('speechpilot')
      console.log('🔗 Protocol client registered:', isRegistered)

    } catch (error) {
      console.error('❌ Failed to register protocol client:', error)
    }

    // 處理 Windows/Linux 的 deep link
    app.on('second-instance', (_event, commandLine, _workingDirectory) => {
      // 有人試圖運行第二個實例，我們應該聚焦到我們的窗口
      const url = commandLine.find(arg => arg.startsWith('speechpilot://'))
      if (url) {
        this.handleDeepLink(url)
      }
    })

    // 處理 macOS 的 deep link
    app.on('open-url', (event, url) => {
      event.preventDefault()
      this.handleDeepLink(url)
    })

    // 檢查啟動時的 deep link (Windows/Linux)
    if (process.platform === 'win32' || process.platform === 'linux') {
      const url = process.argv.find(arg => arg.startsWith('speechpilot://'))
      if (url) {
        // 延遲處理，確保應用程式完全啟動
        setTimeout(() => this.handleDeepLink(url), 1000)
      }
    }
  }

  private handleDeepLink(url: string) {
    console.log('🔗 Received deep link:', url)

    try {
      const urlObj = new URL(url)
      console.log('🔗 Parsed URL:', {
        protocol: urlObj.protocol,
        hostname: urlObj.hostname,
        pathname: urlObj.pathname,
        search: urlObj.search
      })

      // Deep Link 處理（保留以備將來使用）
      console.log('🔗 Deep link received but not currently used for OAuth')
      console.log('ℹ️ OAuth now uses localhost callback instead of deep links')
    } catch (error) {
      console.error('❌ Failed to parse deep link:', error)
    }
  }

  private showUpgradeDialog(remainingSeconds: number) {
    const minutes = Math.floor(remainingSeconds / 60)
    const seconds = remainingSeconds % 60

    const message = remainingSeconds > 0
      ? `您今天還剩 ${minutes}分${seconds}秒使用時間。升級以獲得更多使用時間！`
      : '您的使用時間已用完。升級以繼續使用 SpeechPilot！'

    console.log('🔔 升級提示:', message)

    // 這裡可以顯示系統通知或對話框
    // 暫時使用 console.log，之後可以整合到 UI 中
  }

  private setupIPC() {
    // 處理錄音窗口關閉事件
    ipcMain.on('close-recording-window', () => {
      console.log('🪟 Recording window close requested by user, stopping recording...')
      if (this.isRecording) {
        this.stopRecording()
      }
    })

    // 處理錄音窗口關閉事件 (舊的事件名，保持兼容性)
    ipcMain.on('recording-window-closed', () => {
      console.log('🪟 Recording window closed by user, stopping recording...')
      if (this.isRecording) {
        this.stopRecording()
      }
    })

    // 監聽設定變更
    ipcMain.on('settings-changed', (_settings) => {
      console.log('⚙️ Settings changed, reloading hotkeys and languages...')
      this.setupHotkeys()
      this.updateAIServiceLanguages()
    })
  }

  private toggleRecording(mode: 'ai' | 'direct') {
    console.log(`🔄 Toggle recording - Current state: ${this.isRecording ? 'Recording' : 'Stopped'}`)
    
    if (this.isRecording) {
      // 如果正在錄音，停止錄音
      console.log('⏹️ Stopping recording...')
      this.stopRecording()
    } else {
      // 如果沒有錄音，開始錄音
      console.log(`🎙️ Starting ${mode} recording...`)
      this.startRecording(mode)
    }
  }

  private async startRecording(mode: 'ai' | 'direct') {
    console.log(`🎙️ Attempting to start ${mode} mode recording...`)

    try {
      // 檢查認證狀態
      const authState = this.serviceManager.getAuthState()

      if (!authState.isAuthenticated) {
        console.log('🔐 用戶未登入，顯示登入視窗')
        try {
          const loginSuccess = await this.loginWindow.create()
          if (!loginSuccess) {
            console.log('❌ 用戶取消登入或登入失敗')
            return
          }
          console.log('✅ 用戶登入成功')
        } catch (error) {
          console.error('❌ 登入過程中發生錯誤:', error)
          return
        }
      }

      // 進行使用量檢查
      const features = mode === 'ai' ? ['ai-speech-to-text'] : ['direct-speech-to-text']
      const estimatedDuration = 120 // 預估 2 分鐘

      const canStart = await this.serviceManager.startUsageSession(features, estimatedDuration)

      if (!canStart) {
        console.log('❌ 無法開始錄音 - 使用量檢查失敗')
        return
      }

      this.isRecording = true
      console.log(`🎙️ Starting ${mode} mode - press hotkey again to stop...`)

      // 創建並顯示錄音窗口
      this.recordingWindow.create()
      this.recordingWindow.updateStatus({
        isRecording: true,
        mode: mode,
        status: 'recording'
      })

      // 直接錄音到內存，不保存文件
      await this.startDirectAudioCapture(mode)

    } catch (error) {
      console.error('❌ Failed to start recording:', error)
      this.isRecording = false

      // 如果錄音窗口已創建，更新狀態
      if (this.recordingWindow.isVisible()) {
        this.recordingWindow.updateStatus({
          isRecording: false,
          status: 'error',
          message: '錄音啟動失敗'
        })
        this.recordingWindow.close(2000)
      }

      // 結束使用會話
      await this.serviceManager.endUsageSession()
    }
  }

  private async stopRecording() {
    console.log('⏹️ User requested to stop recording...')

    // 通知錄音窗口更新停止按鈕狀態
    if (this.recordingWindow.isVisible()) {
      const window = this.recordingWindow.getWindow()
      if (window && !window.isDestroyed()) {
        window.webContents.executeJavaScript(`
          if (typeof updateStopButtonState === 'function') {
            updateStopButtonState('stopping');
          }
        `).catch(err => console.log('Failed to update stop button state:', err))
      }
    }

    if (this.recordingProcess) {
      // AI 模式：發送 SIGINT 信號給 FFmpeg 進行優雅停止
      this.recordingProcess.kill('SIGINT')
      console.log('📝 Stop signal sent to FFmpeg')
    } else {
      // Direct 模式：停止 WebSocket 連接
      this.aiService.stopRealtimeTranscription()
      this.isRecording = false

      // 立即更新按鈕為已停止狀態
      if (this.recordingWindow.isVisible()) {
        const window = this.recordingWindow.getWindow()
        if (window && !window.isDestroyed()) {
          window.webContents.executeJavaScript(`
            if (typeof updateStopButtonState === 'function') {
              setTimeout(() => updateStopButtonState('stopped'), 800);
            }
          `).catch(err => console.log('Failed to update stop button state:', err))
        }
      }

      // 更新錄音窗口狀態並關閉
      this.recordingWindow.updateStatus({
        isRecording: false,
        status: 'completed',
        message: '錄音已停止'
      })

      // 工作流程 3: 計算使用量
      await this.endRecordingSession()

      // 延遲關閉窗口，讓用戶看到"已停止"狀態
      this.recordingWindow.close(1500)

      console.log('⚠️ Stopped realtime transcription and closed window')
    }

    console.log('✅ Recording stop requested')
  }

  private async startDirectAudioCapture(mode: 'ai' | 'direct') {
    console.log(`🎤 Starting direct audio capture for ${mode} mode...`)
    console.log('🔧 Using bundled FFmpeg for high-quality audio capture...')

    // 重置文字輸入狀態，清除上一輪的緩存
    this.textInputService.resetStreamingState()
    console.log('🔄 Reset text input state for new recording')

    // 獲取音頻設備設定
    const settings = this.settingsWindow.getSettings()
    let audioDevice = settings.audioDevice || 'default'

    // 如果設定為 default，則使用自動檢測的設備
    if (audioDevice === 'default') {
      audioDevice = await this.audioService.getSelectedAudioDevice()
    }

    if (!audioDevice) {
      console.error('❌ No audio input device found')
      this.isRecording = false
      this.recordingWindow.updateStatus({
        isRecording: false,
        status: 'error',
        message: '未找到音頻輸入設備'
      })
      this.recordingWindow.close(2000)
      return
    }

    if (mode === 'direct') {
      // 直接轉錄模式：立即開始實時 WebSocket 串流
      console.log('🔄 Starting real-time WebSocket streaming...')
      await this.startRealtimeStreaming(audioDevice)
    } else {
      // AI 模式：錄音到內存後處理
      await this.startBufferedRecording(audioDevice, mode)
    }
  }

  private async startRealtimeStreaming(audioDevice: string) {
    try {
      // 獲取設定中的靜音超時時間
      const settings = this.settingsWindow.getSettings()
      const recordingTimeoutSeconds = settings.recordingTimeout || 5

      // Direct 模式的靜音檢測變數
      let lastActivityTime = Date.now()
      let silenceCheckInterval: NodeJS.Timeout | null = null

      // 每秒檢查是否超過靜音時間
      silenceCheckInterval = setInterval(() => {
        const silenceDuration = (Date.now() - lastActivityTime) / 1000
        if (silenceDuration > recordingTimeoutSeconds) {
          console.log(`⏰ Direct mode: ${recordingTimeoutSeconds} seconds of silence detected, stopping...`)
          if (silenceCheckInterval) {
            clearInterval(silenceCheckInterval)
          }
          this.stopRecording()
        }
      }, 1000)

      // 立即連接 WebSocket 並開始實時串流
      await this.aiService.startRealtimeTranscription(
        audioDevice,
        this.audioService,
        // 部分轉錄回調
        (partialText: string) => {
          console.log('🔄 Real-time partial transcription:', partialText)
          this.recordingWindow.updateStatus({
            isRecording: true,  // 保持錄音狀態，讓計時器繼續運行
            status: 'processing',
            message: `轉錄中: ${partialText.substring(partialText.length - 20)}...`
          })
        },
        // 最終轉錄回調
        (finalText: string) => {
          console.log('✅ Real-time final transcription:', finalText)
          // 清除靜音檢查計時器
          if (silenceCheckInterval) {
            clearInterval(silenceCheckInterval)
          }
          this.recordingWindow.updateStatus({
            status: 'completed',
            message: '轉錄完成'
          })
        },
        // 錄音停止回調
        () => {
          this.isRecording = false
          this.recordingWindow.close(1000)
        },
        // 音量更新回調
        (volume: number) => {
          this.recordingWindow.updateStatus({
            volume: volume
          })
        },
        // 活動檢測回調
        () => {
          lastActivityTime = Date.now()
        },
        // 靜音超時設定
        recordingTimeoutSeconds
      )
    } catch (error) {
      console.error('❌ Failed to start realtime streaming:', error)
      this.isRecording = false
      this.recordingWindow.updateStatus({
        isRecording: false,
        status: 'error',
        message: '實時串流啟動失敗'
      })
      this.recordingWindow.close(2000)
    }
  }

  private async startBufferedRecording(audioDevice: string, mode: 'ai' | 'direct') {
    // 用於收集音頻數據的緩衝區
    const audioChunks: Buffer[] = []

    // 獲取錄音超時設定
    const settings = this.settingsWindow.getSettings()
    const recordingTimeoutSeconds = settings.recordingTimeout || 5

    // AI 模式的超時機制
    let silenceTimer: NodeJS.Timeout | null = null
    const SILENCE_TIMEOUT = recordingTimeoutSeconds * 1000 // 轉換為毫秒

    // 使用 AudioService 創建錄音進程
    try {
      this.recordingProcess = this.audioService.createRecordingProcess(audioDevice)
    } catch (error) {
      console.error('❌ Failed to create recording process:', error)
      this.isRecording = false
      this.recordingWindow.updateStatus({
        isRecording: false,
        status: 'error',
        message: 'FFmpeg 未正確打包'
      })
      this.recordingWindow.close(2000)
      return
    }

    // 收集音頻數據並實時檢測音量
    let totalBytes = 0
    this.recordingProcess.stdout?.on('data', (chunk: Buffer) => {
      audioChunks.push(chunk)
      totalBytes += chunk.length

      // 實時音量檢測
      if (chunk.length > 44) { // 確保有足夠的數據
        const pcmData = chunk.subarray(44) // 跳過 WAV 標頭
        if (pcmData.length > 0) {
          const samples = new Int16Array(pcmData.buffer, pcmData.byteOffset, pcmData.length / 2)
          const maxAmplitude = Math.max(...Array.from(samples).map(Math.abs))
          const volume = Math.min(100, Math.round((maxAmplitude / 32767) * 100))

          // 發送音量更新到錄音窗口
          this.recordingWindow.updateStatus({
            volume: volume
          })

          // AI 模式的靜音檢測和超時機制
          if (mode === 'ai') {
            const volumeThreshold = settings.volume?.threshold || 10
            if (volume > volumeThreshold) { // 有聲音
              // 清除現有的靜音計時器
              if (silenceTimer) {
                clearTimeout(silenceTimer)
                silenceTimer = null
              }
            } else { // 靜音
              // 如果還沒有設置靜音計時器，設置一個
              if (!silenceTimer) {
                silenceTimer = setTimeout(() => {
                  console.log(`⏰ AI mode: ${recordingTimeoutSeconds} seconds of silence detected, auto-stopping...`)
                  this.recordingWindow.updateStatus({
                    status: 'processing',
                    message: '檢測到靜音，自動停止錄音...'
                  })
                  this.stopRecording()
                }, SILENCE_TIMEOUT)
              }
            }
          }
        }
      }

      // 每秒顯示一次進度 (假設每秒約 32KB 數據)
      if (totalBytes % 32000 < chunk.length) {
        const seconds = Math.floor(totalBytes / 32000)
        console.log(`🎤 Recording: ${seconds}s (${Math.round(totalBytes / 1024)}KB)`)
      }
    })

    // 監聽 FFmpeg 的錯誤輸出
    this.recordingProcess.stderr?.on('data', (data: Buffer) => {
      const errorMsg = data.toString()
      // 只顯示重要的錯誤信息，過濾掉正常的進度信息
      if (!errorMsg.includes('size=') && !errorMsg.includes('time=') && !errorMsg.includes('bitrate=')) {
        console.log('🔧 FFmpeg stderr:', errorMsg.trim())
      }
    })

    this.recordingProcess.on('close', async (code: number | null) => {
      console.log(`🎙️ Recording finished with code: ${code}`)

      // 檢查是否有音頻數據，不管退出代碼如何
      if (audioChunks.length > 0) {
        // 合併所有音頻塊
        const audioBuffer = Buffer.concat(audioChunks)
        console.log(`📁 Total audio size: ${audioBuffer.length} bytes`)

        // 檢查音頻數據是否足夠（至少 1 秒的數據）
        if (audioBuffer.length > 32000) { // 約 1 秒的 16kHz 單聲道音頻
          try {
            // 更新窗口狀態為處理中
            this.recordingWindow.updateStatus({
              isRecording: false,
              status: 'processing',
              message: mode === 'ai' ? '正在進行 AI 處理...' : '正在轉錄音頻...'
            })

            let result: string

            // 只有 AI 模式會進入 startBufferedRecording
            // 直接轉錄模式使用 startRealtimeStreaming，不會到這裡
            result = await this.aiService.processWithAI(audioBuffer)

            console.log('✅ AI Processing completed:', result.substring(0, 100) + '...')

            // 更新窗口狀態為完成
            this.recordingWindow.updateStatus({
              status: 'completed',
              message: '處理完成，正在輸入文字...'
            })

            await this.textInputService.inputText(result)

            // 工作流程 3: 計算使用量
            await this.endRecordingSession()

            // 關閉窗口
            this.recordingWindow.close(1000)

          } catch (error) {
            console.error('❌ Processing error:', error)
            this.recordingWindow.updateStatus({
              status: 'error',
              message: `處理失敗：${(error as Error).message}`
            })
            this.recordingWindow.close(3000)
          }
        } else {
          console.log('⚠️ Audio data too short, skipping processing')
          this.recordingWindow.updateStatus({
            status: 'error',
            message: '錄音時間太短，請重試'
          })
          this.recordingWindow.close(2000)
        }
      } else {
        console.log('❌ No audio data captured')
        this.recordingWindow.updateStatus({
          status: 'error',
          message: '未捕獲到音頻數據，請重試'
        })
        this.recordingWindow.close(2000)
      }

      this.isRecording = false

      // 確保結束使用會話
      await this.endRecordingSession()
    })

    this.recordingProcess.on('error', (error: any) => {
      console.error('❌ FFmpeg recording error:', error)
      this.isRecording = false
      this.recordingWindow.updateStatus({
        isRecording: false,
        status: 'error',
        message: '音頻錄製失敗'
      })
      this.recordingWindow.close(2000)
    })

    console.log('🎤 FFmpeg recording started - press hotkey again to stop')
  }

  // 工作流程 3: 結束錄音會話並計算使用量
  private async endRecordingSession() {
    try {
      const result = await this.serviceManager.endUsageSession()

      if (result.success) {
        const minutes = Math.floor((result.secondsUsed || 0) / 60)
        const seconds = (result.secondsUsed || 0) % 60
        console.log(`📊 使用量記錄完成: ${minutes}分${seconds}秒, Tokens: ${result.tokensUsed || 0}`)
      } else {
        console.error('❌ 使用量記錄失敗:', result.error)
      }
    } catch (error) {
      console.error('❌ 結束錄音會話失敗:', error)
    }
  }

  destroy() {
    globalShortcut.unregisterAll()
    if (this.recordingProcess) {
      this.recordingProcess.kill('SIGTERM')
    }
    if (existsSync(this.tempAudioFile)) {
      unlinkSync(this.tempAudioFile)
    }
    this.tray?.destroy()
    this.recordingWindow.close(0)
  }
}

// 應用程式啟動
let speechPilot: SpeechPilot

app.whenReady().then(async () => {
  speechPilot = new SpeechPilot()
  await speechPilot.init()
})

app.on('window-all-closed', () => {
  // 保持運行
})

app.on('before-quit', () => {
  speechPilot?.destroy()
})

// 防止多個實例
const gotTheLock = app.requestSingleInstanceLock()
if (!gotTheLock) {
  app.quit()
}
