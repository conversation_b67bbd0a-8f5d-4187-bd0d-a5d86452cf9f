import { spawn } from 'child_process'
import { platform } from 'os'

export class TextInputService {
  private lastInputText = ''

  async inputText(text: string): Promise<void> {
    try {
      console.log('⌨️ Inputting text:', text.substring(0, 50) + '...')
      
      const cleanText = text.replace(/[""]/g, '"').replace(/\r?\n/g, ' ')
      
      return new Promise((resolve, reject) => {
        let process: any

        if (platform() === 'darwin') {
          // macOS - 使用 osascript
          const script = `tell application "System Events" to keystroke "${cleanText.replace(/"/g, '\\"')}"`
          process = spawn('osascript', ['-e', script])
        } else {
          // Windows - 使用剪貼板方式避免輸入法問題
          const script = `
            Add-Type -AssemblyName System.Windows.Forms
            # 保存當前剪貼板內容
            $originalClipboard = [System.Windows.Forms.Clipboard]::GetText()
            # 設置新的剪貼板內容
            [System.Windows.Forms.Clipboard]::SetText("${cleanText.replace(/"/g, '""')}")
            Start-Sleep -Milliseconds 100
            # 發送 Ctrl+V 來貼上
            [System.Windows.Forms.SendKeys]::SendWait("^v")
            Start-Sleep -Milliseconds 100
            # 恢復原始剪貼板內容
            if ($originalClipboard) {
              [System.Windows.Forms.Clipboard]::SetText($originalClipboard)
            } else {
              [System.Windows.Forms.Clipboard]::Clear()
            }
          `
          process = spawn('powershell', ['-Command', script])
        }

        process.on('close', (code: number) => {
          if (code === 0) {
            console.log('✅ Text input completed successfully')
            resolve(undefined)
          } else {
            console.error(`❌ Text input process exited with code: ${code}`)
            reject(new Error(`Text input failed with code: ${code}`))
          }
        })

        process.on('error', (error: Error) => {
          console.error('❌ Text input process error:', error)
          reject(error)
        })
      })
      
    } catch (error) {
      console.error('❌ Text input failed:', error)
      throw error
    }
  }

  async inputTextStreaming(text: string): Promise<void> {
    try {
      // 只輸入新增的文字部分
      if (text.length <= this.lastInputText.length) {
        return // 沒有新內容
      }

      const newText = text.substring(this.lastInputText.length)

      // 如果新文字太短，跳過（避免頻繁輸入單個字符）
      if (newText.trim().length < 2) {
        return
      }

      console.log('⌨️ Streaming input:', newText.substring(0, 30) + '...')

      const cleanText = newText.replace(/[""]/g, '"').replace(/\r?\n/g, ' ')

      if (platform() === 'darwin') {
        // macOS - 使用 osascript
        const script = `tell application "System Events" to keystroke "${cleanText.replace(/"/g, '\\"')}"`
        const process = spawn('osascript', ['-e', script])
        await new Promise((resolve) => {
          process.on('close', resolve)
        })
      } else {
        // Windows - 使用 PowerShell，進一步減少延遲
        const script = `
          Add-Type -AssemblyName System.Windows.Forms
          Start-Sleep -Milliseconds 20
          [System.Windows.Forms.SendKeys]::SendWait("${cleanText.replace(/"/g, '""')}")
        `
        const process = spawn('powershell', ['-Command', script])
        await new Promise((resolve) => {
          process.on('close', resolve)
        })
      }

      this.lastInputText = text
      console.log('✅ Streaming text input completed')

    } catch (error) {
      console.error('❌ Streaming text input failed:', error)
      throw error
    }
  }

  resetStreamingState(): void {
    this.lastInputText = ''
  }
}
